# 数据库: mini_market

**分析时间**: 2025/7/24 18:11:26

**表数量**: 11

**表列表**: tb_coin_app_market, tb_coin_market, tb_coin_market_off_market, tb_coin_market_optional, tb_coin_market_theme, tb_coin_turnover, tb_off_market_template, tb_off_market_template_detail, tb_push_topic_info, tb_usdt_contract_coin_market, undo_log

---

## 表名: tb_coin_app_market

**表注释**: 设置不同应用、服务返回的币对

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| app_type | int(10) | 否 |  | 应用类型：1极速版  2专业版 |  |  |
| service_type | int(10) | 否 |  | 服务类型：1现货交易 2合约交易 |  |  |
| market | varchar(36) | 否 |  | 币对 |  |  |
| status | int(10) | 否 |  | 状态 1上线 2下架 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: tb_coin_market

**表注释**: 交易对信息

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 主键id | PRI | auto_increment |
| coin_market | varchar(30) | 否 |  | 交易对名称 | UNI |  |
| init_price | decimal(30,16) | 否 | 0.0000000000000000 | 首发价 |  |  |
| price_precision | int(10) | 否 | 4 | 价格精度 |  |  |
| amount_precision | int(10) | 否 | 4 | 数量精度 |  |  |
| ex_rate_buy | decimal(20,10) | 否 | 0.0000000000 | 买入交易手续费率 |  |  |
| ex_rate_sell | decimal(20,10) | 否 | 0.0000000000 | 卖出交易手续费率 |  |  |
| min_trade_limit | decimal(30,16) | 否 | 0.0000000000000000 | 交易最小下单量 |  |  |
| max_trade_limit | decimal(30,16) | 否 | 1000.0000000000000000 | 交易最大下单量 |  |  |
| gears | tinyint(3) | 是 | 20 | 下单的档位 |  |  |
| coin_id | int(10) | 否 |  | 基础币 | MUL |  |
| market_id | int(10) | 否 |  | 计价币 |  |  |
| sort_no | int(10) | 否 | 0 | 排序值 |  |  |
| status | tinyint(3) | 否 | 1 | 状态0下架 1上架  |  |  |
| on_top | tinyint(3) | 否 | 0 | 是否置顶  0不置顶 1置顶 |  |  |
| on_dealing | tinyint(3) | 否 | 1 | 是否开启交易 1开启 0关闭 |  |  |
| main_stream | tinyint(3) | 否 | 0 | 是否是主流币 0 不是 1是 |  |  |
| partition_sort | tinyint(3) | 否 | 0 | 分区排序 |  |  |
| on_hot | tinyint(3) | 是 | 0 | 是否热门  0否 1是 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| base_quote | coin_id | 是 | BTREE |
| base_quote | market_id | 是 | BTREE |
| PRIMARY | id | 是 | BTREE |
| symbol | coin_market | 是 | BTREE |
| symbol_index | coin_market | 否 | BTREE |

---

## 表名: tb_coin_market_off_market

**表注释**: 开休市管理

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| coin_market_id | int(10) | 否 |  | 交易对id | UNI |  |
| open_market_type | int(10) | 否 |  | 开市方式 0手动 1自动 |  |  |
| close_market_type | int(10) | 否 |  | 休市方式 0手动 1自动 |  |  |
| status | int(10) | 否 |  | 当前开市状态 0关闭 1开启 |  |  |
| template_id | int(10) | 否 |  | 自动开休市时间模板 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| index_coin_market_id | coin_market_id | 是 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: tb_coin_market_optional

**表注释**: 商户订单回调表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键ID 递增 | PRI | auto_increment |
| user_id | int(10) | 否 | 0 | 用户userId |  |  |
| uid | int(10) | 否 |  | 用户UID | MUL |  |
| coin_market_id | text(65535) | 是 |  | 交易对ID |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 最后修改时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uid_index | uid | 否 | BTREE |

---

## 表名: tb_coin_market_theme

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| name_cn | varchar(255) | 是 |  | 中文名称 |  |  |
| name_hk | varchar(255) | 是 |  | 繁体名称 |  |  |
| name_en | varchar(255) | 是 |  | 英文名称 |  |  |
| sort | int(10) | 是 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: tb_coin_turnover

**表注释**: 成交量浮动表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| coin_market | varchar(30) | 是 |  | 交易对 |  |  |
| coin_type | tinyint(3) | 是 |  | 交易对类型(0-合约 1-币币) |  |  |
| turnover_upper | decimal(30,16) | 是 |  | 成交额上限 |  |  |
| turnover_lower | decimal(30,16) | 是 |  | 成交额下限 |  |  |
| proportion | varchar(55) | 是 |  | 占比 |  |  |
| create_time | datetime | 是 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| update_time | datetime | 是 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: tb_off_market_template

**表注释**: 开休市模板名称

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| name | varchar(255) | 否 |  | 模板名称 | UNI |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uni_name | name | 是 | BTREE |

---

## 表名: tb_off_market_template_detail

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 是 | 0 | 无注释 |  |  |
| template_id | int(10) | 是 |  | 无注释 |  |  |
| open_time | timestamp | 是 |  | 无注释 |  |  |
| close_time | timestamp | 是 |  | 无注释 |  |  |
| update_time | timestamp | 是 |  | 无注释 |  |  |
| create_time | timestamp | 是 |  | 无注释 |  |  |

---

## 表名: tb_push_topic_info

**表注释**: websocket topic 信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| topic | varchar(100) | 否 |  | topic名称 | UNI |  |
| rmq_topic | varchar(100) | 否 |  | rmq topic 名称 | MUL |  |
| orderly | tinyint(3) | 否 | 0 | 0无序消费，1顺序消费 |  |  |
| broadcasting | tinyint(3) | 否 | 0 | 0集群消费，1广播消费 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 最后修改时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| rmq_topic | rmq_topic | 否 | BTREE |
| topic | topic | 是 | BTREE |

---

## 表名: tb_usdt_contract_coin_market

**表注释**: 合约交易对信息

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 主键id | PRI | auto_increment |
| coin_market | varchar(30) | 否 |  | 交易对名称 | UNI |  |
| init_price | decimal(20,10) | 否 | 0.0000000000 | 首发价 |  |  |
| price_precision | int(10) | 否 | 4 | 价格精度 |  |  |
| amount_precision | int(10) | 否 | 4 | 数量精度 |  |  |
| face_value | decimal(32,16) | 是 | 0.0000000000000000 | 合约面值 |  |  |
| taker_fee | decimal(32,16) | 是 | 0.0000000000000000 | Taker手续费率 |  |  |
| coin_id | int(10) | 否 |  | 基础币 | MUL |  |
| market_id | int(10) | 否 |  | 计价币 |  |  |
| market_coin_name | varchar(20) | 是 |  | 计价货币名称 |  |  |
| base_coin_name | varchar(20) | 是 |  | 基础货币名称 |  |  |
| sort_no | int(10) | 否 | 0 | 排序值 |  |  |
| status | tinyint(3) | 否 | 0 | 状态0下架 1上架  |  |  |
| on_dealing | tinyint(3) | 否 | 0 | 是否开启交易 1开启 0关闭 |  |  |
| price_floating_limit | double(4,4) | 否 | 0.0000 | 委托价格浮动限制 |  |  |
| gears | tinyint(3) | 否 | 5 | 市价档位 |  |  |
| off_market_status | int(10) | 否 | 0 | 有无开休市状态 0无 1有 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| base_quote | coin_id | 是 | BTREE |
| base_quote | market_id | 是 | BTREE |
| PRIMARY | id | 是 | BTREE |
| symbol | coin_market | 是 | BTREE |
| symbol_index | coin_market | 否 | BTREE |

---

## 表名: undo_log

**表注释**: seata分布式事务控制表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| branch_id | bigint(19) | 否 |  | 无注释 |  |  |
| xid | varchar(100) | 否 |  | 无注释 | MUL |  |
| context | varchar(128) | 否 |  | 无注释 |  |  |
| rollback_info | longblob(4294967295) | 否 |  | 无注释 |  |  |
| log_status | int(10) | 否 |  | 无注释 |  |  |
| log_created | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| log_modified | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| ext | varchar(100) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| ux_undo_log | xid | 是 | BTREE |
| ux_undo_log | branch_id | 是 | BTREE |

---

