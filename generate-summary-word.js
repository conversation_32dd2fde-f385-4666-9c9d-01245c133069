const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
const { Document, Packer, Paragraph, Table, TableRow, TableCell, WidthType, AlignmentType, HeadingLevel, TextRun, BorderStyle } = require('docx');

// 数据库连接配置
const dbConfig = {
  host: '*************',
  port: 3306,
  user: 'root',
  password: 'Nod%D*h2y2*oUZuz',
  charset: 'utf8mb4'
};

// 获取所有数据库
async function getAllDatabases(connection) {
  try {
    const [rows] = await connection.query('SHOW DATABASES');
    const systemDbs = ['information_schema', 'performance_schema', 'mysql', 'sys'];
    const databases = rows
      .map(row => row.Database)
      .filter(db => !systemDbs.includes(db) && !db.toLowerCase().includes('mock'));
    
    return databases;
  } catch (error) {
    console.error('获取数据库列表失败:', error);
    return [];
  }
}

// 获取数据库中的所有表
async function getTablesInDatabase(connection, database) {
  try {
    await connection.query(`USE \`${database}\``);
    const [rows] = await connection.query('SHOW TABLES');
    const tableKey = `Tables_in_${database}`;
    return rows.map(row => row[tableKey]);
  } catch (error) {
    console.error(`获取数据库 ${database} 的表列表失败:`, error);
    return [];
  }
}

// 检查缺少注释的字段
async function checkMissingComments(connection, database, tableName) {
  try {
    const [columns] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        COLUMN_COMMENT,
        COLUMN_KEY
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [database, tableName]);

    const [tableInfo] = await connection.execute(`
      SELECT TABLE_COMMENT 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    `, [database, tableName]);

    const missingComments = columns.filter(col => 
      !col.COLUMN_COMMENT || col.COLUMN_COMMENT.trim() === '' || col.COLUMN_COMMENT === '无注释'
    );

    return {
      database,
      tableName,
      tableComment: tableInfo[0]?.TABLE_COMMENT || '',
      totalColumns: columns.length,
      missingComments: missingComments.length,
      missingCommentFields: missingComments
    };
  } catch (error) {
    console.error(`检查表 ${database}.${tableName} 注释失败:`, error);
    return null;
  }
}

// 创建表格行
function createTableRow(cells, isHeader = false) {
  return new TableRow({
    children: cells.map(cell => new TableCell({
      children: [new Paragraph({
        children: [new TextRun({
          text: String(cell),
          bold: isHeader,
          size: isHeader ? 24 : 20
        })],
        alignment: AlignmentType.CENTER
      })],
      width: {
        size: 100 / cells.length,
        type: WidthType.PERCENTAGE,
      },
    }))
  });
}

// 生成Word文档
async function generateSummaryWordDocument() {
  let connection;
  
  try {
    console.log('开始连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功!');
    
    const databases = await getAllDatabases(connection);
    console.log(`发现数据库: ${databases.join(', ')}`);
    
    const allMissingCommentsData = [];
    let totalTables = 0;
    let totalTablesWithMissingComments = 0;
    let totalMissingFields = 0;
    
    // 收集所有数据
    for (const database of databases) {
      console.log(`\n检查数据库: ${database}`);
      
      const tables = await getTablesInDatabase(connection, database);
      console.log(`发现 ${tables.length} 个表`);
      
      for (const tableName of tables) {
        console.log(`  检查表: ${tableName}`);
        const missingData = await checkMissingComments(connection, database, tableName);
        if (missingData) {
          allMissingCommentsData.push(missingData);
          totalTables++;
          if (missingData.missingComments > 0) {
            totalTablesWithMissingComments++;
            totalMissingFields += missingData.missingComments;
          }
        }
      }
    }
    
    // 创建Word文档
    const docElements = [];
    
    // 文档标题
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: 'MySQL数据库分析总结报告',
        bold: true,
        size: 36
      })],
      heading: HeadingLevel.TITLE,
      alignment: AlignmentType.CENTER,
      spacing: { after: 400 }
    }));
    
    // 基本信息
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `分析时间: ${new Date().toLocaleString()}`,
        size: 24
      })],
      spacing: { after: 200 }
    }));
    
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `数据库服务器: ${dbConfig.host}:${dbConfig.port}`,
        size: 24
      })],
      spacing: { after: 400 }
    }));
    
    // 总体统计
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: '总体统计',
        bold: true,
        size: 32
      })],
      heading: HeadingLevel.HEADING_1,
      spacing: { before: 400, after: 200 }
    }));
    
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `• 总数据库数: ${databases.length}`,
        size: 24
      })],
      spacing: { after: 100 }
    }));
    
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `• 总表数: ${totalTables}`,
        size: 24
      })],
      spacing: { after: 100 }
    }));
    
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `• 有缺失注释的表数: ${totalTablesWithMissingComments}`,
        size: 24
      })],
      spacing: { after: 100 }
    }));
    
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: `• 缺失注释的字段总数: ${totalMissingFields}`,
        size: 24
      })],
      spacing: { after: 400 }
    }));
    
    // 各数据库统计表格
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: '各数据库统计',
        bold: true,
        size: 32
      })],
      heading: HeadingLevel.HEADING_1,
      spacing: { before: 400, after: 200 }
    }));
    
    const databaseSummary = {};
    allMissingCommentsData.forEach(item => {
      if (!item) return;
      
      if (!databaseSummary[item.database]) {
        databaseSummary[item.database] = {
          tables: 0,
          tablesWithMissing: 0,
          missingFields: 0
        };
      }
      
      databaseSummary[item.database].tables++;
      if (item.missingComments > 0) {
        databaseSummary[item.database].tablesWithMissing++;
        databaseSummary[item.database].missingFields += item.missingComments;
      }
    });
    
    const summaryRows = [
      createTableRow(['数据库', '总表数', '有缺失注释的表数', '缺失注释字段数'], true)
    ];
    
    Object.keys(databaseSummary).forEach(db => {
      const summary = databaseSummary[db];
      summaryRows.push(createTableRow([
        db,
        summary.tables,
        summary.tablesWithMissing,
        summary.missingFields
      ]));
    });
    
    docElements.push(new Table({
      rows: summaryRows,
      width: {
        size: 100,
        type: WidthType.PERCENTAGE,
      },
      borders: {
        top: { style: BorderStyle.SINGLE, size: 1 },
        bottom: { style: BorderStyle.SINGLE, size: 1 },
        left: { style: BorderStyle.SINGLE, size: 1 },
        right: { style: BorderStyle.SINGLE, size: 1 },
        insideHorizontal: { style: BorderStyle.SINGLE, size: 1 },
        insideVertical: { style: BorderStyle.SINGLE, size: 1 },
      },
    }));
    
    // 数据库详细列表
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: '数据库详细信息',
        bold: true,
        size: 32
      })],
      heading: HeadingLevel.HEADING_1,
      spacing: { before: 600, after: 200 }
    }));
    
    for (const database of databases) {
      const tables = await getTablesInDatabase(connection, database);
      
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `${database} 数据库`,
          bold: true,
          size: 28
        })],
        heading: HeadingLevel.HEADING_2,
        spacing: { before: 400, after: 200 }
      }));
      
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `表数量: ${tables.length}`,
          size: 22
        })],
        spacing: { after: 100 }
      }));
      
      const dbSummary = databaseSummary[database];
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `缺失注释的表: ${dbSummary.tablesWithMissing}`,
          size: 22
        })],
        spacing: { after: 100 }
      }));
      
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `缺失注释的字段: ${dbSummary.missingFields}`,
          size: 22
        })],
        spacing: { after: 200 }
      }));
      
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: `表列表: ${tables.join(', ')}`,
          size: 20
        })],
        spacing: { after: 300 }
      }));
    }
    
    // 建议和说明
    docElements.push(new Paragraph({
      children: [new TextRun({
        text: '建议和说明',
        bold: true,
        size: 32
      })],
      heading: HeadingLevel.HEADING_1,
      spacing: { before: 600, after: 200 }
    }));
    
    const suggestions = [
      '1. 建议为所有缺失注释的字段补充适当的注释',
      '2. 制定数据库开发规范，要求新建表必须添加完整注释',
      '3. 定期检查数据库注释的完整性',
      '4. 使用提供的SQL脚本批量补充缺失的注释',
      '5. 注释应该简洁明了，准确描述字段的用途和含义'
    ];
    
    suggestions.forEach(suggestion => {
      docElements.push(new Paragraph({
        children: [new TextRun({
          text: suggestion,
          size: 22
        })],
        spacing: { after: 150 }
      }));
    });
    
    // 创建文档
    const doc = new Document({
      sections: [{
        properties: {},
        children: docElements
      }]
    });
    
    // 保存文档
    const outputPath = path.join('./database-analysis', 'database-analysis-summary.docx');
    const buffer = await Packer.toBuffer(doc);
    await fs.writeFile(outputPath, buffer);
    
    console.log(`\n总结Word文档已生成: ${outputPath}`);
    console.log('文档生成完成!');
    
  } catch (error) {
    console.error('生成Word文档过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行生成
if (require.main === module) {
  generateSummaryWordDocument().catch(console.error);
}

module.exports = { generateSummaryWordDocument };
