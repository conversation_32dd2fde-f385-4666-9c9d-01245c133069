# 数据库: mini_config

**分析时间**: 2025/7/24 18:11:25

**表数量**: 12

**表列表**: config_info, config_info_aggr, config_info_beta, config_info_tag, config_tags_relation, group_capacity, his_config_info, permissions, roles, tenant_capacity, tenant_info, users

---

## 表名: config_info

**表注释**: config_info

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| data_id | varchar(255) | 否 |  | data_id | MUL |  |
| group_id | varchar(255) | 是 |  | 无注释 |  |  |
| content | longtext(4294967295) | 否 |  | content |  |  |
| md5 | varchar(32) | 是 |  | md5 |  |  |
| gmt_create | datetime | 否 | 2010-05-05 00:00:00 | 创建时间 |  |  |
| gmt_modified | datetime | 否 | 2010-05-05 00:00:00 | 修改时间 |  |  |
| src_user | text(65535) | 是 |  | source user |  |  |
| src_ip | varchar(20) | 是 |  | source ip |  |  |
| app_name | varchar(128) | 是 |  | 无注释 |  |  |
| tenant_id | varchar(128) | 是 |  | 租户字段 |  |  |
| c_desc | varchar(256) | 是 |  | 无注释 |  |  |
| c_use | varchar(64) | 是 |  | 无注释 |  |  |
| effect | varchar(64) | 是 |  | 无注释 |  |  |
| type | varchar(64) | 是 |  | 无注释 |  |  |
| c_schema | text(65535) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uk_configinfo_datagrouptenant | data_id | 是 | BTREE |
| uk_configinfo_datagrouptenant | group_id | 是 | BTREE |
| uk_configinfo_datagrouptenant | tenant_id | 是 | BTREE |

---

## 表名: config_info_aggr

**表注释**: 增加租户字段

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| data_id | varchar(255) | 否 |  | data_id | MUL |  |
| group_id | varchar(255) | 否 |  | group_id |  |  |
| datum_id | varchar(255) | 否 |  | datum_id |  |  |
| content | longtext(4294967295) | 否 |  | 内容 |  |  |
| gmt_modified | datetime | 否 |  | 修改时间 |  |  |
| app_name | varchar(128) | 是 |  | 无注释 |  |  |
| tenant_id | varchar(128) | 是 |  | 租户字段 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uk_configinfoaggr_datagrouptenantdatum | data_id | 是 | BTREE |
| uk_configinfoaggr_datagrouptenantdatum | group_id | 是 | BTREE |
| uk_configinfoaggr_datagrouptenantdatum | tenant_id | 是 | BTREE |
| uk_configinfoaggr_datagrouptenantdatum | datum_id | 是 | BTREE |

---

## 表名: config_info_beta

**表注释**: config_info_beta

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| data_id | varchar(255) | 否 |  | data_id | MUL |  |
| group_id | varchar(128) | 否 |  | group_id |  |  |
| app_name | varchar(128) | 是 |  | app_name |  |  |
| content | longtext(4294967295) | 否 |  | content |  |  |
| beta_ips | varchar(1024) | 是 |  | betaIps |  |  |
| md5 | varchar(32) | 是 |  | md5 |  |  |
| gmt_create | datetime | 否 | 2010-05-05 00:00:00 | 创建时间 |  |  |
| gmt_modified | datetime | 否 | 2010-05-05 00:00:00 | 修改时间 |  |  |
| src_user | text(65535) | 是 |  | source user |  |  |
| src_ip | varchar(20) | 是 |  | source ip |  |  |
| tenant_id | varchar(128) | 是 |  | 租户字段 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uk_configinfobeta_datagrouptenant | data_id | 是 | BTREE |
| uk_configinfobeta_datagrouptenant | group_id | 是 | BTREE |
| uk_configinfobeta_datagrouptenant | tenant_id | 是 | BTREE |

---

## 表名: config_info_tag

**表注释**: config_info_tag

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| data_id | varchar(255) | 否 |  | data_id | MUL |  |
| group_id | varchar(128) | 否 |  | group_id |  |  |
| tenant_id | varchar(128) | 是 |  | tenant_id |  |  |
| tag_id | varchar(128) | 否 |  | tag_id |  |  |
| app_name | varchar(128) | 是 |  | app_name |  |  |
| content | longtext(4294967295) | 否 |  | content |  |  |
| md5 | varchar(32) | 是 |  | md5 |  |  |
| gmt_create | datetime | 否 | 2010-05-05 00:00:00 | 创建时间 |  |  |
| gmt_modified | datetime | 否 | 2010-05-05 00:00:00 | 修改时间 |  |  |
| src_user | text(65535) | 是 |  | source user |  |  |
| src_ip | varchar(20) | 是 |  | source ip |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uk_configinfotag_datagrouptenanttag | data_id | 是 | BTREE |
| uk_configinfotag_datagrouptenanttag | group_id | 是 | BTREE |
| uk_configinfotag_datagrouptenanttag | tenant_id | 是 | BTREE |
| uk_configinfotag_datagrouptenanttag | tag_id | 是 | BTREE |

---

## 表名: config_tags_relation

**表注释**: config_tag_relation

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | MUL |  |
| tag_name | varchar(128) | 否 |  | tag_name |  |  |
| tag_type | varchar(64) | 是 |  | tag_type |  |  |
| data_id | varchar(255) | 否 |  | data_id |  |  |
| group_id | varchar(128) | 否 |  | group_id |  |  |
| tenant_id | varchar(128) | 是 |  | tenant_id | MUL |  |
| nid | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_tenant_id | tenant_id | 否 | BTREE |
| PRIMARY | nid | 是 | BTREE |
| uk_configtagrelation_configidtag | id | 是 | BTREE |
| uk_configtagrelation_configidtag | tag_name | 是 | BTREE |
| uk_configtagrelation_configidtag | tag_type | 是 | BTREE |

---

## 表名: group_capacity

**表注释**: 集群、各Group容量信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(20) | 否 |  | 主键ID | PRI | auto_increment |
| group_id | varchar(128) | 否 |  | Group ID，空字符表示整个集群 | UNI |  |
| quota | int(10) | 否 | 0 | 配额，0表示使用默认值 |  |  |
| usage | int(10) | 否 | 0 | 使用量 |  |  |
| max_size | int(10) | 否 | 0 | 单个配置大小上限，单位为字节，0表示使用默认值 |  |  |
| max_aggr_count | int(10) | 否 | 0 | 聚合子配置最大个数，，0表示使用默认值 |  |  |
| max_aggr_size | int(10) | 否 | 0 | 单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值 |  |  |
| max_history_count | int(10) | 否 | 0 | 最大变更历史数量 |  |  |
| gmt_create | datetime | 否 | 2010-05-05 00:00:00 | 创建时间 |  |  |
| gmt_modified | datetime | 否 | 2010-05-05 00:00:00 | 修改时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uk_group_id | group_id | 是 | BTREE |

---

## 表名: his_config_info

**表注释**: 多租户改造

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(20) | 否 |  | 无注释 |  |  |
| nid | bigint(20) | 否 |  | 无注释 | PRI | auto_increment |
| data_id | varchar(255) | 否 |  | 无注释 | MUL |  |
| group_id | varchar(128) | 否 |  | 无注释 |  |  |
| app_name | varchar(128) | 是 |  | app_name |  |  |
| content | longtext(4294967295) | 否 |  | 无注释 |  |  |
| md5 | varchar(32) | 是 |  | 无注释 |  |  |
| gmt_create | datetime | 否 | 2010-05-05 00:00:00 | 无注释 | MUL |  |
| gmt_modified | datetime | 否 | 2010-05-05 00:00:00 | 无注释 | MUL |  |
| src_user | text(65535) | 是 |  | 无注释 |  |  |
| src_ip | varchar(20) | 是 |  | 无注释 |  |  |
| op_type | char(10) | 是 |  | 无注释 |  |  |
| tenant_id | varchar(128) | 是 |  | 租户字段 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_did | data_id | 否 | BTREE |
| idx_gmt_create | gmt_create | 否 | BTREE |
| idx_gmt_modified | gmt_modified | 否 | BTREE |
| PRIMARY | nid | 是 | BTREE |

---

## 表名: permissions

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| role | varchar(50) | 否 |  | 无注释 | PRI |  |
| resource | varchar(512) | 否 |  | 无注释 | PRI |  |
| action | varchar(8) | 否 |  | 无注释 | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| uk_role_permission | role | 是 | BTREE |
| uk_role_permission | resource | 是 | BTREE |
| uk_role_permission | action | 是 | BTREE |

---

## 表名: roles

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| username | varchar(50) | 否 |  | 无注释 | PRI |  |
| role | varchar(50) | 否 |  | 无注释 | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| uk_username_role | username | 是 | BTREE |
| uk_username_role | role | 是 | BTREE |

---

## 表名: tenant_capacity

**表注释**: 租户容量信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(20) | 否 |  | 主键ID | PRI | auto_increment |
| tenant_id | varchar(128) | 否 |  | Tenant ID | UNI |  |
| quota | int(10) | 否 | 0 | 配额，0表示使用默认值 |  |  |
| usage | int(10) | 否 | 0 | 使用量 |  |  |
| max_size | int(10) | 否 | 0 | 单个配置大小上限，单位为字节，0表示使用默认值 |  |  |
| max_aggr_count | int(10) | 否 | 0 | 聚合子配置最大个数 |  |  |
| max_aggr_size | int(10) | 否 | 0 | 单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值 |  |  |
| max_history_count | int(10) | 否 | 0 | 最大变更历史数量 |  |  |
| gmt_create | datetime | 否 | 2010-05-05 00:00:00 | 创建时间 |  |  |
| gmt_modified | datetime | 否 | 2010-05-05 00:00:00 | 修改时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uk_tenant_id | tenant_id | 是 | BTREE |

---

## 表名: tenant_info

**表注释**: tenant_info

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| kp | varchar(128) | 否 |  | kp | MUL |  |
| tenant_id | varchar(128) | 是 |  | tenant_id | MUL |  |
| tenant_name | varchar(128) | 是 |  | tenant_name |  |  |
| tenant_desc | varchar(256) | 是 |  | tenant_desc |  |  |
| create_source | varchar(32) | 是 |  | create_source |  |  |
| gmt_create | bigint(19) | 否 |  | 创建时间 |  |  |
| gmt_modified | bigint(19) | 否 |  | 修改时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_tenant_id | tenant_id | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |
| uk_tenant_info_kptenantid | kp | 是 | BTREE |
| uk_tenant_info_kptenantid | tenant_id | 是 | BTREE |

---

## 表名: users

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| username | varchar(50) | 否 |  | 无注释 | PRI |  |
| password | varchar(500) | 否 |  | 无注释 |  |  |
| enabled | tinyint(3) | 否 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | username | 是 | BTREE |

---

