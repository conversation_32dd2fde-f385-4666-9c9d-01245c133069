# 数据库: mini_assets

**分析时间**: 2025/7/24 18:11:24

**表数量**: 27

**表列表**: assets_account_change_log, assets_account_info, assets_balance_frozen, assets_chain_fee_record, assets_chain_fee_relation, assets_collection_relation, assets_colletion_record, assets_deposit_record, assets_tenant_wallet_config, assets_transfer_step_log, assets_wallet_book_info, assets_wallet_change_log, assets_wallet_frozen, assets_wallet_information, assets_withdraw_apply, assets_withdraw_coin_exemption_cfg, assets_withdraw_limit, chain_coin_description, chain_coin_info, chain_contract_template, chain_info, chain_info_copy, chain_protocol_info, dms_wallet_address_modify_log, market_coin_price_cfg, undo_log, user_app_info

---

## 表名: assets_account_change_log

**表注释**: 账户变动日志

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| change_log_id | bigint(19) | 否 |  | 使用雪花算法生成主键 | PRI |  |
| account_id | bigint(19) | 否 |  | 无注释 |  |  |
| change_type | int(10) | 否 |  | 1-充值；2-提现； |  |  |
| relation_id | bigint(19) | 否 |  | 没有填0 |  |  |
| relation_type | int(10) | 否 |  | 1-充值记录；2-提现记录；3-链费记录；没有填0 |  |  |
| before_amount | decimal(30,16) | 否 |  | 无注释 |  |  |
| amount | decimal(30,16) | 否 |  | 无注释 |  |  |
| after_amount | decimal(30,16) | 否 |  | 无注释 |  |  |
| token_id | varchar(256) | 是 |  | 如果是NFT，必填 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| create_by | varchar(64) | 是 |  | 登录管理端的用户名，程序操作填0 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | change_log_id | 是 | BTREE |

---

## 表名: assets_account_info

**表注释**: 币种账户余额。商户配置币种时，生成商户账户信息。客户创建钱包同时生成账户信息。

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| account_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| tenant_id | bigint(19) | 是 |  | 商户ID |  |  |
| app_id | bigint(19) | 是 |  | AppID |  |  |
| chain | varchar(32) | 否 |  | 链名称 |  |  |
| protocol | varchar(32) | 是 |  | 协议名称 |  |  |
| protocol_type | int(10) | 否 | 0 | 协议类型：0-无;1-FT;2-NFT |  |  |
| coin | varchar(32) | 是 |  | 币种名称 |  |  |
| token_id | varchar(256) | 是 |  | NFT的TokenId |  |  |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |
| user_id | varchar(32) | 否 |  | 如果用户类型为客户，则保存客户ID，如果客户类型为商户，则保存商户ID |  |  |
| user_type | int(10) | 否 |  | 1-客户；2-商户 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 无注释 |  |  |
| frozen_amount | decimal(36,18) | 否 | 0.000000000000000000 | 冻结金额 |  |  |
| status | int(10) | 否 | 0 | 0-禁用;1-启用 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | account_id | 是 | BTREE |

---

## 表名: assets_balance_frozen

**表注释**: 记录客户/商户提现冻结的商户出入金热钱包

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| frozen_id | bigint(19) | 否 |  | 主键 | PRI |  |
| account_id | bigint(19) | 否 |  | 无注释 |  |  |
| relation_id | bigint(19) | 否 |  | 客户/商户提现表 |  |  |
| relation_type | int(10) | 否 |  | 无注释 |  |  |
| frozen_type | int(10) | 否 | 0 | 冻结业务类型:1-提币数量；2-提币手续费 |  |  |
| amount | decimal(36,18) | 否 |  | 负值往张本上减 |  |  |
| status | int(10) | 否 | 1 | 1-冻结中；2-已解冻 |  |  |
| unfreeze_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | frozen_id | 是 | BTREE |

---

## 表名: assets_chain_fee_record

**表注释**: 链费转账记录

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| record_id | bigint(19) | 否 |  | 无注释 | PRI |  |
| transaction_id | varchar(128) | 否 |  | 交易号 |  |  |
| chain | varchar(32) | 否 |  | 链 |  |  |
| protocol | varchar(32) | 是 |  | 协议 |  |  |
| coin | varchar(32) | 是 |  | 币种 |  |  |
| from_wallet_id | bigint(19) | 否 |  | 源钱包ID |  |  |
| from_address | varchar(256) | 否 |  | 源地址 |  |  |
| to_wallet_id | bigint(19) | 否 |  | 目标钱包ID |  |  |
| to_address | varchar(256) | 否 |  | 目标地址 |  |  |
| qty | decimal(36,18) | 否 |  | 数量 |  |  |
| txid | varchar(256) | 是 |  | TXID |  |  |
| status | int(10) | 否 |  | 状态：1-待确认；2-已确认；3-确认失败 |  |  |
| confirm_num | int(10) | 是 |  | 区块确认数 |  |  |
| confirm_time | timestamp | 是 |  | 确认时间 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 链费 |  |  |
| transation_type | int(10) | 是 |  | 转账类型:1-充币；2-提币； |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| gas_limit | decimal(36,18) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | record_id | 是 | BTREE |

---

## 表名: assets_chain_fee_relation

**表注释**: 一条链费记录，可对应多条充值记录，多条提现记录

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| relation_id | bigint(19) | 否 |  | 关系标识 | PRI |  |
| relate_id | bigint(19) | 否 |  | 关联记录ID |  |  |
| chain_fee_record_id | bigint(19) | 否 |  | 链费交易记录 |  |  |
| relate_type | int(10) | 否 |  | 关联类型：1-充币；2-提币 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | relation_id | 是 | BTREE |

---

## 表名: assets_collection_relation

**表注释**: 一条归集记录，可对应多条充值记录。

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| relation_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| relate_id | bigint(19) | 否 |  | 无注释 |  |  |
| collection_record_id | bigint(19) | 否 |  | 无注释 |  |  |
| relate_type | int(10) | 否 |  | 1-归集 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | relation_id | 是 | BTREE |

---

## 表名: assets_colletion_record

**表注释**: 归集记录

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| collection_record_id | bigint(19) | 否 |  | 无注释 | PRI |  |
| transaction_id | varchar(128) | 否 |  | 交易号 |  |  |
| chain | varchar(32) | 否 |  | 链名称 |  |  |
| protocol | varchar(32) | 是 |  | 协议名称 |  |  |
| protocol_type | int(10) | 否 | 1 | 协议类型:0-无;1-FT;2-NFT |  |  |
| coin | varchar(32) | 是 |  | 币种名称 |  |  |
| token_id | varchar(256) | 是 |  | TokenId:如果是NFT，必填 |  |  |
| from_wallet_id | bigint(19) | 否 |  | 来源钱包ID |  |  |
| from_address | varchar(256) | 否 |  | 来源地址 |  |  |
| to_wallet_id | bigint(19) | 是 |  | 目标钱包ID |  |  |
| to_address | varchar(256) | 是 |  | 目标地址 |  |  |
| qty | decimal(36,18) | 否 |  | 数量 |  |  |
| txid | varchar(256) | 是 |  | 交易hash(TxID) |  |  |
| status | int(10) | 否 | 0 | 状态:1-待确认；2-已确认；3-确认失败 |  |  |
| confirm_time | timestamp | 是 |  | 确认时间 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 链费 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| gas_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | collection_record_id | 是 | BTREE |

---

## 表名: assets_deposit_record

**表注释**: 记录业务相关的记录

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| record_id | bigint(19) | 否 |  | 使用雪花算法生成主键ID | PRI |  |
| wallet_id | bigint(19) | 否 |  | 无注释 |  |  |
| user_id | varchar(32) | 否 |  | 如果用户类型为客户，则保存客户ID，如果客户类型为商户，则保存商户ID |  |  |
| user_type | int(10) | 否 |  | 1-客户；2-商户 |  |  |
| transaction_id | varchar(128) | 否 |  | 关联充值业务ID |  |  |
| transaction_type | int(10) | 否 |  | 1-客户充值；2-找零；3-链费充值；6-系统mint nft 产生的充值订单 |  |  |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| protocol_type | int(10) | 否 |  | 0-无;1-FT;2-NFT |  |  |
| coin | varchar(32) | 是 |  | 无注释 |  |  |
| token_id | varchar(256) | 是 |  | 如果是NFT，必填 |  |  |
| from_address | varchar(256) | 否 |  | 无注释 |  |  |
| to_address | varchar(256) | 否 |  | 无注释 |  |  |
| amount | decimal(36,18) | 否 |  | 无注释 |  |  |
| txid | varchar(256) | 否 |  | 无注释 |  |  |
| status | int(10) | 否 |  | 1-待确认；2-已确认；3-确认失败； |  |  |
| confirm_num | int(10) | 是 |  | 无注释 |  |  |
| confirm_time | timestamp | 是 |  | 无注释 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 无注释 |  |  |
| collection_status | int(10) | 否 | 0 | -1-无需归集；0-未归集；1-归集中；2-已归集 |  |  |
| collection_fee_status | int(10) | 否 | 0 | -1-无需归集；0-未打链费；1-已打链费；2-链费充足；4-链费处理中 |  |  |
| callback_status | int(10) | 否 | 0 | -1-无需回调；0-未回调；1-已回调；2-回调中 |  |  |
| callback_time | timestamp | 是 |  | 无注释 |  |  |
| keep_accounts_status | int(10) | 是 |  | 0-未上账；1-已上账 |  |  |
| step | int(10) | 否 |  | 1-充值；2-打手续费；3-归集；4-完成 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |
| nft_from | int(10) | 否 | 1 | NFT来源：1-充值；2-Mint |  |  |
| nft_id | bigint(19) | 是 |  | 关联nft_item表的主键 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | record_id | 是 | BTREE |

---

## 表名: assets_tenant_wallet_config

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| tenant_coin_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| app_id | bigint(19) | 否 |  | 无注释 |  |  |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| coin | varchar(32) | 否 |  | 无注释 |  |  |
| is_base_coin | int(10) | 是 |  | 是否原生币种：0-非原生币种；1-是原生币种 |  |  |
| coin_id | bigint(19) | 否 |  | 无注释 |  |  |
| contract_addr | varchar(128) | 是 |  | 无注释 |  |  |
| collection_waterline | decimal(36,18) | 否 | 0.000000000000000000 | 无注释 |  |  |
| hot_wallet_toplimit | decimal(36,18) | 否 | 0.000000000000000000 | 无注释 |  |  |
| hot_wallet_warn_limit | decimal(36,18) | 否 | 0.000000000000000000 | 无注释 |  |  |
| gas_fee_warn_limit | decimal(36,18) | 否 |  | 无注释 |  |  |
| cold_wallet_toplimit | decimal(36,18) | 是 | 0.000000000000000000 | 无注释 |  |  |
| min_deposit_amount | decimal(36,18) | 否 | 0.000000000000000000 | 最小充币/上账数量 |  |  |
| min_collection_amount | decimal(36,18) | 否 | 0.000000000000000000 | 无注释 |  |  |
| withdraw_fee | decimal(36,18) | 否 | 0.000000000000000000 | 提币手续费按固定值 |  |  |
| is_collection | int(10) | 否 | 1 | 0-不归集;1-归集 |  |  |
| status | int(10) | 否 | 1 | 0-禁用;1-可用 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | tenant_coin_id | 是 | BTREE |

---

## 表名: assets_transfer_step_log

**表注释**: 交易步骤日志表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| his_step_id | bigint(19) | 否 |  | 无注释 | PRI |  |
| transaction_id | varchar(128) | 否 |  | 业务ID |  |  |
| business_type | int(10) | 否 |  | 1-充值记录；2-客户提现记录；3-商户提现记录 |  |  |
| step | int(10) | 否 |  | 步骤 |  |  |
| step_status | int(10) | 否 |  | 状态 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | his_step_id | 是 | BTREE |

---

## 表名: assets_wallet_book_info

**表注释**: 钱包子帐表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| wallet_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| wallet_info_id | bigint(19) | 否 |  | 钱包信息ID |  |  |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| protocol_type | int(10) | 否 | 0 | 0-无;1-FT;2-NFT |  |  |
| coin | varchar(32) | 是 |  | 如果是NFT，可空 |  |  |
| token_id | varchar(256) | 是 |  | NFT的TokenId |  |  |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 无注释 |  |  |
| frozen_amount | decimal(36,18) | 否 | 0.000000000000000000 | 冻结数量 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| update_user | varchar(64) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | wallet_id | 是 | BTREE |

---

## 表名: assets_wallet_change_log

**表注释**: 商户钱包变动日志

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| change_log_id | bigint(19) | 否 |  | 变动标识 | PRI |  |
| wallet_id | bigint(19) | 否 |  | 无注释 |  |  |
| change_type | int(10) | 否 |  | 变动类型：1-充值；2-提现； |  |  |
| relation_id | bigint(19) | 否 |  | 关联ID，没有填0 |  |  |
| relation_type | int(10) | 否 |  | 关联业务类型：1-充值记录；2-提现记录；3-链费记录；没有填0 |  |  |
| before_amount | decimal(36,18) | 否 |  | 无注释 |  |  |
| amount | decimal(36,18) | 否 |  | 无注释 |  |  |
| after_amount | decimal(36,18) | 否 |  | 无注释 |  |  |
| from_address | varchar(256) | 否 |  | 无注释 |  |  |
| token_id | varchar(256) | 是 |  | 如果是NFT，必填 |  |  |
| to_address | varchar(256) | 否 |  | 无注释 |  |  |
| txid | varchar(256) | 是 |  | TXID |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| create_by | varchar(64) | 否 | 0 | 登录管理端的用户名 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | change_log_id | 是 | BTREE |

---

## 表名: assets_wallet_frozen

**表注释**: 钱包资金冻结表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| frozen_id | bigint(19) | 否 |  | 冻结标识 | PRI |  |
| wallet_id | bigint(19) | 否 |  | 钱包ID |  |  |
| relation_id | bigint(19) | 否 |  | 客户/商户提现表 |  |  |
| relation_type | int(10) | 否 |  | 无注释 |  |  |
| frozen_type | int(10) | 否 | 0 | 冻结业务类型:1-提币数量；2-提币手续费 |  |  |
| amount | decimal(36,18) | 否 |  | 无注释 |  |  |
| status | int(10) | 否 | 1 | 1-冻结中；2-已解冻 |  |  |
| unfreeze_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | frozen_id | 是 | BTREE |

---

## 表名: assets_wallet_information

**表注释**: 钱包信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| wallet_info_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |
| app_id | bigint(19) | 否 |  | 无注释 |  |  |
| chain | varchar(32) | 否 |  | 无注释 | MUL |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| wallet_type | int(10) | 否 |  | 0-客户钱包；1-出入金热钱包;2-链费钱包;3-冷钱包 |  |  |
| wallet_address | varchar(256) | 是 |  | 无注释 |  |  |
| wallet_name | varchar(256) | 否 |  | 无注释 |  |  |
| chain_wallet_name | varchar(256) | 是 |  | 链上名称 |  |  |
| user_id | varchar(32) | 否 |  | 如果用户类型为客户，则保存客户ID，如果客户类型为商户，则保存商户ID |  |  |
| user_type | int(10) | 否 |  | 1-客户；2-商户 |  |  |
| priority | int(10) | 否 | 1 | 1;2;3... |  |  |
| status | int(10) | 否 | 0 | 0-禁用;1-启用 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| update_user | varchar(64) | 是 |  | 无注释 |  |  |
| wallet_id | bigint(19) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | wallet_info_id | 是 | BTREE |
| unique_chain_address_name | chain | 是 | BTREE |
| unique_chain_address_name | wallet_address | 是 | BTREE |
| unique_chain_address_name | wallet_name | 是 | BTREE |

---

## 表名: assets_withdraw_apply

**表注释**: 客户/商户提现申请表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| apply_id | bigint(19) | 否 |  | 提现申请ID | PRI |  |
| tenant_id | bigint(19) | 否 | 0 | 商户ID |  |  |
| app_id | bigint(19) | 否 |  | AppID |  |  |
| transaction_id | varchar(128) | 否 |  | 商户系统提交 |  |  |
| chain | varchar(32) | 否 |  | 链 |  |  |
| protocol | varchar(32) | 是 |  | 协议 |  |  |
| protocol_type | int(10) | 否 |  | 协议类型：0-无;1-FT;2-NFT |  |  |
| coin | varchar(32) | 是 |  | 币种 |  |  |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |
| token_id | varchar(256) | 是 |  | 如果是NFT，必填 |  |  |
| apply_user_type | int(10) | 是 |  | 申请人类型:1-客户；2-商户 |  |  |
| apply_user | varchar(32) | 否 |  | 申请人：如果为客户，则填写客户ID；如果为商户，则填写商户管理员名称 |  |  |
| from_wallet_id | bigint(19) | 否 |  | 提现钱包地址ID |  |  |
| from_address | varchar(256) | 否 |  | 提现钱包地址 |  |  |
| out_wallet_id | bigint(19) | 是 |  | 出金钱包地址ID |  |  |
| out_address | varchar(256) | 是 |  | 出金钱包地址 |  |  |
| to_address | varchar(256) | 否 |  | 目标钱包地址 |  |  |
| amount | decimal(36,18) | 否 |  | 提现金额 |  |  |
| apply_reason | varchar(1024) | 是 |  | 提现原因 |  |  |
| apply_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| auto_audit | int(10) | 否 |  | 0-自动审核；1-人工审核 |  |  |
| audit_user | varchar(64) | 是 |  | 审核人 |  |  |
| audit_reason | varchar(1024) | 是 |  | 审核原因 |  |  |
| audit_time | timestamp | 是 |  | 无注释 |  |  |
| status | int(10) | 否 | 0 | 0-待审核；1-审核通过；2-审核拒绝；4-待录入；5-录入成功 |  |  |
| txid | varchar(256) | 是 |  | 无注释 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 链费 |  |  |
| withdraw_fee_status | int(10) | 否 | 0 | -1-无需归集；0-未打链费；1-已打链费；2-链费充足；3-链费处理中;4-链费处理失败 |  |  |
| withdraw_status | int(10) | 否 | 0 | 0-未处理；1-待确认；2-已确认；3-确认失败； |  |  |
| withdraw_time | timestamp | 是 |  | 无注释 |  |  |
| callback_status | int(10) | 否 | 0 | 0-未回调；1-已回调 |  |  |
| callback_time | timestamp | 是 |  | 无注释 |  |  |
| step | int(10) | 否 | 0 | 0-待审核；1-打手续费；2-链上出账；3-出账成功；4-出账失败 |  |  |
| fee_speed | varchar(255) | 否 | average | 无注释 |  |  |
| gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| gas_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| gas_rate | int(10) | 否 | 2 | 上链速率：1-慢；2-中；3-快 |  |  |
| nft_id | bigint(19) | 是 |  | 无注释 |  |  |
| batch_txid | varchar(256) | 否 | NORMAL | NORMAL-正常提现；其他-批量发送NFT(UUID) |  |  |
| batch_send_time | timestamp | 是 |  | 无注释 |  |  |
| withdraw_type | tinyint(3) | 否 | 0 | 提现类型：0-待确认，1-热提，2-冷提 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | apply_id | 是 | BTREE |

---

## 表名: assets_withdraw_coin_exemption_cfg

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| tenant_id | bigint(19) | 否 |  | 用户类型:1-客户；2-商户 |  |  |
| day_exemption_times | int(10) | 否 |  | 单日免审限次 |  |  |
| day_exemption_quota | decimal(36,18) | 否 |  | 单日免审限额 |  |  |
| day_times | int(10) | 否 |  | 单日限次 |  |  |
| day_quota | decimal(36,18) | 否 |  | 单日限额 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | datetime | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | datetime | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: assets_withdraw_limit

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| limit_id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| tenant_coin_id | bigint(19) | 否 |  | 无注释 |  |  |
| is_limit | int(10) | 否 | 1 | 0-不启用；1-启用 |  |  |
| is_audit | int(10) | 否 | 1 | 客户提现是否需要审核:0-不审核；1-审核 |  |  |
| min_withdraw_amount | decimal(36,18) | 是 |  | 无注释 |  |  |
| max_withdraw_amount_once | decimal(36,18) | 是 |  | 无注释 |  |  |
| max_withdraw_amount_day | decimal(36,18) | 是 |  | 无注释 |  |  |
| day_start_time | time | 是 |  | 无注释 |  |  |
| day_end_time | time | 是 |  | 无注释 |  |  |
| day_once_audit_free | decimal(36,18) | 是 |  | 无注释 |  |  |
| day_one_audit_num_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| day_one_audit_amount_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| day_all_audit_amount_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| night_start_time | time | 是 |  | 无注释 |  |  |
| night_end_time | time | 是 |  | 无注释 |  |  |
| night_once_audit_free | decimal(36,18) | 是 |  | 无注释 |  |  |
| night_one_audit_num_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| night_one_audit_amount_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| night_all_audit_amount_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | limit_id | 是 | BTREE |

---

## 表名: chain_coin_description

**表注释**: 币种的充提币说明

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| description_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| tenant_coin_id | bigint(19) | 是 |  | 无注释 |  |  |
| coin_id | bigint(19) | 否 |  | 无注释 |  |  |
| description_type | int(10) | 否 |  | 1-充币；2-提币；3.... |  |  |
| content | text(65535) | 是 |  | 无注释 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| language | varchar(32) | 否 |  | 语言 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | description_id | 是 | BTREE |

---

## 表名: chain_coin_info

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| coin_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| chain | varchar(32) | 否 |  | 无注释 | MUL |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| coin | varchar(32) | 否 |  | 无注释 |  |  |
| coin_icon | varchar(128) | 是 |  | 无注释 |  |  |
| contract_addr | varchar(128) | 是 |  | 无注释 |  |  |
| coin_type | int(10) | 否 |  | 1-coin(币);2-token(代币) |  |  |
| show_precision | int(10) | 否 |  | 数值显示精度 |  |  |
| gas_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| official_website | varchar(128) | 是 |  | 查询网址 |  |  |
| is_deposit | int(10) | 否 | 1 | 0-不允许;1-允许 |  |  |
| is_withdraw | int(10) | 否 | 1 | 0-不允许;1-允许 |  |  |
| status | int(10) | 否 | 0 | 0-禁用;1-启用 |  |  |
| has_tags | int(10) | 否 |  | 0-无标签；1-有标签 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| min_collection | decimal(36,18) | 否 | 0.000000000000000000 | 最小归集数量 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| chain_coin | chain | 是 | BTREE |
| chain_coin | coin | 是 | BTREE |
| PRIMARY | coin_id | 是 | BTREE |

---

## 表名: chain_contract_template

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| template_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| protocol | varchar(32) | 否 |  | 无注释 |  |  |
| contract_name | varchar(128) | 否 |  | 无注释 |  |  |
| contract_type | int(10) | 否 | 1 | 1-标准型 |  |  |
| chain | varchar(32) | 否 |  | 链 |  |  |
| protocol_type | int(10) | 否 |  | 0-无;1-FT;2-NFT |  |  |
| status | int(10) | 否 |  | 0-禁用;1-可用 |  |  |
| deploy_gas_limit | decimal(36,18) | 否 |  | Deploy gas limit |  |  |
| mint_gas_limit | decimal(36,18) | 否 |  | mint gas limit |  |  |
| transfer_gas_limit | decimal(36,18) | 否 |  | transfer gas limit |  |  |
| contract_class_name | varchar(128) | 否 |  | 无注释 |  |  |
| supply_type | int(10) | 否 | 1 | 供应量类型：1-有最大量；2-没有 |  |  |
| mint_type | int(10) | 否 | 1 | mint权限： 1-只允许owner；2-允许任何人 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | template_id | 是 | BTREE |

---

## 表名: chain_info

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| chain_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| base_coin | varchar(32) | 否 |  | 无注释 |  |  |
| safe_confirms | int(10) | 否 | 6 | 无注释 |  |  |
| fast_confirms | int(10) | 否 | 1 | 无注释 |  |  |
| has_token | int(10) | 否 | 0 | 0-无;1-有 |  |  |
| status | int(10) | 否 | 0 | 0-禁用;1-可用 |  |  |
| gas_tracker_api | varchar(128) | 否 |  | 无注释 |  |  |
| chain_list | int(10) | 否 |  | 链网络驱动类型 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| low_gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| average_gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| high_gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| addr_regexp | varchar(128) | 是 |  | 地址正则 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | chain_id | 是 | BTREE |

---

## 表名: chain_info_copy

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| chain_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| base_coin | varchar(32) | 否 |  | 无注释 |  |  |
| safe_confirms | int(10) | 否 | 6 | 无注释 |  |  |
| fast_confirms | int(10) | 否 | 1 | 无注释 |  |  |
| has_token | int(10) | 否 | 0 | 0-无;1-有 |  |  |
| status | int(10) | 否 | 0 | 0-禁用;1-可用 |  |  |
| gas_tracker_api | varchar(128) | 否 |  | 无注释 |  |  |
| chain_list | int(10) | 否 |  | 链网络驱动类型 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| low_gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| average_gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| high_gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| addr_regexp | varchar(128) | 是 |  | 地址正则 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | chain_id | 是 | BTREE |

---

## 表名: chain_protocol_info

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| protocol_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| protocol | varchar(32) | 否 |  | 无注释 |  |  |
| protocol_type | int(10) | 否 |  | 0-无;1-FT;2-NFT |  |  |
| status | int(10) | 否 | 0 | 0-禁用;1-可用 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | protocol_id | 是 | BTREE |

---

## 表名: dms_wallet_address_modify_log

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 主键 | PRI | auto_increment |
| old_wallet_info_id | bigint(19) | 否 |  | 无注释 |  |  |
| new_wallet_info_id | bigint(19) | 是 |  | 无注释 |  |  |
| old_tenant_id | bigint(19) | 否 |  | 无注释 |  |  |
| new_tenant_id | bigint(19) | 是 |  | 无注释 |  |  |
| old_app_id | bigint(19) | 否 |  | 无注释 |  |  |
| new_app_id | bigint(19) | 是 |  | 无注释 |  |  |
| old_chain | varchar(32) | 否 |  | 无注释 |  |  |
| new_chain | varchar(32) | 是 |  | 无注释 |  |  |
| old_wallet_address | varchar(256) | 否 |  | 无注释 |  |  |
| new_wallet_address | varchar(256) | 是 |  | 无注释 |  |  |
| old_wallet_type | int(10) | 否 |  | 无注释 |  |  |
| new_wallet_type | int(10) | 是 |  | 无注释 |  |  |
| old_user_id | varchar(32) | 否 |  | 无注释 |  |  |
| new_user_id | varchar(32) | 是 |  | 无注释 |  |  |
| create_by | varchar(128) | 否 |  | 无注释 |  |  |
| create_time | datetime | 否 |  | 无注释 |  |  |
| update_by | varchar(128) | 否 |  | 无注释 |  |  |
| update_time | datetime | 否 |  | 无注释 |  |  |
| status | tinyint(3) | 否 | 0 | 0-未处理，1-已处理 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: market_coin_price_cfg

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| coin | varchar(32) | 否 |  | 币种 |  |  |
| price | decimal(36,18) | 否 |  | 价格 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | datetime | 否 |  | 创建时间 |  | on update CURRENT_TIMESTAMP |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | datetime | 否 |  | 更新时间 |  | on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: undo_log

**表注释**: seata分布式事务控制表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| branch_id | bigint(19) | 否 |  | 无注释 |  |  |
| xid | varchar(100) | 否 |  | 无注释 | MUL |  |
| context | varchar(128) | 否 |  | 无注释 |  |  |
| rollback_info | longblob(4294967295) | 否 |  | 无注释 |  |  |
| log_status | int(10) | 否 |  | 无注释 |  |  |
| log_created | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| log_modified | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| ext | varchar(100) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| ux_undo_log | xid | 是 | BTREE |
| ux_undo_log | branch_id | 是 | BTREE |

---

## 表名: user_app_info

**表注释**: 商户App信息表，只保留基本信息，用于关联使用名称查询，其他配置在mini库

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| app_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |
| app_name | varchar(128) | 否 |  | App名称 |  |  |
| app_icon | varchar(128) | 是 |  | AppIcon |  |  |
| status | int(10) | 否 | 0 | 0-无效；1-有效 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | app_id | 是 | BTREE |

---

