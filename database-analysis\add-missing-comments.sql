-- MySQL数据库字段注释补充SQL脚本
-- 生成时间: 2025/7/24 18:15:35
-- 数据库服务器: 192.168.0.101:3306

-- 使用说明:
-- 1. 请仔细检查每个建议的注释是否准确
-- 2. 根据实际业务需求修改注释内容
-- 3. 执行前请备份数据库
-- 4. 建议分批执行，避免长时间锁表

-- ========================================

-- ========================================
-- 数据库: mini
-- ========================================

-- 数据库: mini
-- 表: sys_client_app
-- 表注释: 授权应用的关联信息

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_client_app` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';

-- 数据库: mini
-- 表: sys_dept
-- 表注释: 部门管理

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_dept` MODIFY COLUMN `dept_id` bigint(19) NOT NULL COMMENT '主键ID';
ALTER TABLE `sys_dept` MODIFY COLUMN `name` varchar(50) NULL COMMENT '名称';
ALTER TABLE `sys_dept` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';
ALTER TABLE `sys_dept` MODIFY COLUMN `parent_id` bigint(19) NULL COMMENT '父级ID';
ALTER TABLE `sys_dept` MODIFY COLUMN `tenant_id` bigint(19) NULL COMMENT '租户ID';

-- 数据库: mini
-- 表: sys_dict
-- 表注释: 字典表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_dict` MODIFY COLUMN `dict_type` varchar(100) NULL COMMENT '类型';
ALTER TABLE `sys_dict` MODIFY COLUMN `description` varchar(100) NULL COMMENT '描述';
ALTER TABLE `sys_dict` MODIFY COLUMN `remarks` varchar(255) NULL COMMENT '备注';
ALTER TABLE `sys_dict` MODIFY COLUMN `system_flag` char(1) NULL DEFAULT '0' COMMENT '请补充注释';
ALTER TABLE `sys_dict` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_dict_item
-- 表注释: 字典项

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_dict_item` MODIFY COLUMN `dict_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `sys_dict_item` MODIFY COLUMN `item_value` varchar(100) NULL COMMENT '值';
ALTER TABLE `sys_dict_item` MODIFY COLUMN `label` varchar(100) NULL COMMENT '文本内容';
ALTER TABLE `sys_dict_item` MODIFY COLUMN `dict_type` varchar(100) NULL COMMENT '类型';
ALTER TABLE `sys_dict_item` MODIFY COLUMN `description` varchar(100) NULL COMMENT '描述';
ALTER TABLE `sys_dict_item` MODIFY COLUMN `remarks` varchar(255) NULL COMMENT '备注';
ALTER TABLE `sys_dict_item` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_file
-- 表注释: 文件管理表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_file` MODIFY COLUMN `file_name` varchar(100) NULL COMMENT '名称';
ALTER TABLE `sys_file` MODIFY COLUMN `bucket_name` varchar(200) NULL COMMENT '名称';
ALTER TABLE `sys_file` MODIFY COLUMN `original` varchar(100) NULL COMMENT '文本内容';
ALTER TABLE `sys_file` MODIFY COLUMN `type` varchar(50) NULL COMMENT '类型';
ALTER TABLE `sys_file` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_log
-- 表注释: 日志表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_log` MODIFY COLUMN `log_type` char(1) NULL DEFAULT '0' COMMENT '类型';
ALTER TABLE `sys_log` MODIFY COLUMN `title` varchar(255) NULL COMMENT '文本内容';
ALTER TABLE `sys_log` MODIFY COLUMN `service_id` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `sys_log` MODIFY COLUMN `remote_addr` varchar(255) NULL COMMENT '文本内容';
ALTER TABLE `sys_log` MODIFY COLUMN `user_agent` varchar(1000) NULL COMMENT '文本内容';
ALTER TABLE `sys_log` MODIFY COLUMN `request_uri` varchar(255) NULL COMMENT '文本内容';
ALTER TABLE `sys_log` MODIFY COLUMN `method` varchar(10) NULL COMMENT '文本内容';
ALTER TABLE `sys_log` MODIFY COLUMN `params` text(65535) NULL COMMENT '文本内容';
ALTER TABLE `sys_log` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';
ALTER TABLE `sys_log` MODIFY COLUMN `exception` text(65535) NULL COMMENT '文本内容';

-- 数据库: mini
-- 表: sys_menu
-- 表注释: 菜单权限表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_menu` MODIFY COLUMN `name` varchar(128) NULL COMMENT '名称';
ALTER TABLE `sys_menu` MODIFY COLUMN `permission` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `sys_menu` MODIFY COLUMN `path` varchar(128) NULL COMMENT '路径';
ALTER TABLE `sys_menu` MODIFY COLUMN `icon` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `sys_menu` MODIFY COLUMN `keep_alive` char(1) NULL DEFAULT '0' COMMENT '请补充注释';
ALTER TABLE `sys_menu` MODIFY COLUMN `menu_type` char(1) NULL DEFAULT '0' COMMENT '类型';
ALTER TABLE `sys_menu` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_oauth_client_details
-- 表注释: 终端信息表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `client_id` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `resource_ids` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `client_secret` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `scope` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `authorized_grant_types` varchar(256) NULL COMMENT '类型';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `web_server_redirect_uri` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `authorities` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `access_token_validity` int(10) NULL COMMENT '令牌';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `refresh_token_validity` int(10) NULL COMMENT '令牌';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `additional_information` varchar(4096) NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `autoapprove` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `sys_oauth_client_details` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_public_param
-- 表注释: 公共参数配置表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_public_param` MODIFY COLUMN `public_name` varchar(128) NULL COMMENT '名称';
ALTER TABLE `sys_public_param` MODIFY COLUMN `public_key` varchar(128) NULL COMMENT '键';
ALTER TABLE `sys_public_param` MODIFY COLUMN `public_value` varchar(128) NULL COMMENT '值';
ALTER TABLE `sys_public_param` MODIFY COLUMN `status` char(1) NULL DEFAULT '0' COMMENT '状态';
ALTER TABLE `sys_public_param` MODIFY COLUMN `validate_code` varchar(64) NULL COMMENT '编码';
ALTER TABLE `sys_public_param` MODIFY COLUMN `public_type` char(1) NULL DEFAULT '0' COMMENT '类型';
ALTER TABLE `sys_public_param` MODIFY COLUMN `system_flag` char(1) NULL DEFAULT '0' COMMENT '请补充注释';
ALTER TABLE `sys_public_param` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_role
-- 表注释: 系统角色表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_role` MODIFY COLUMN `role_id` bigint(19) NOT NULL COMMENT '主键ID';
ALTER TABLE `sys_role` MODIFY COLUMN `role_name` varchar(64) NULL COMMENT '名称';
ALTER TABLE `sys_role` MODIFY COLUMN `role_code` varchar(64) NULL COMMENT '编码';
ALTER TABLE `sys_role` MODIFY COLUMN `role_desc` varchar(255) NULL COMMENT '描述';
ALTER TABLE `sys_role` MODIFY COLUMN `ds_type` char(1) NULL DEFAULT '2' COMMENT '类型';
ALTER TABLE `sys_role` MODIFY COLUMN `ds_scope` varchar(255) NULL COMMENT '文本内容';
ALTER TABLE `sys_role` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';
ALTER TABLE `sys_role` MODIFY COLUMN `tenant_id` bigint(19) NULL COMMENT '租户ID';
ALTER TABLE `sys_role` MODIFY COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';
ALTER TABLE `sys_role` MODIFY COLUMN `update_time` datetime NULL COMMENT '更新时间';

-- 数据库: mini
-- 表: sys_route_conf
-- 表注释: 路由配置表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_route_conf` MODIFY COLUMN `route_name` varchar(30) NULL COMMENT '名称';
ALTER TABLE `sys_route_conf` MODIFY COLUMN `route_id` varchar(30) NULL COMMENT '文本内容';
ALTER TABLE `sys_route_conf` MODIFY COLUMN `uri` varchar(50) NULL COMMENT '文本内容';
ALTER TABLE `sys_route_conf` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_social_details
-- 表注释: 系统社交登录账号表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_social_details` MODIFY COLUMN `type` varchar(16) NULL COMMENT '类型';
ALTER TABLE `sys_social_details` MODIFY COLUMN `remark` varchar(64) NULL COMMENT '备注';
ALTER TABLE `sys_social_details` MODIFY COLUMN `app_id` varchar(64) NULL COMMENT '文本内容';
ALTER TABLE `sys_social_details` MODIFY COLUMN `app_secret` varchar(64) NULL COMMENT '文本内容';
ALTER TABLE `sys_social_details` MODIFY COLUMN `redirect_url` varchar(128) NULL COMMENT '链接地址';
ALTER TABLE `sys_social_details` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_tenant
-- 表注释: 租户表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_tenant` MODIFY COLUMN `name` varchar(255) NULL COMMENT '名称';
ALTER TABLE `sys_tenant` MODIFY COLUMN `code` varchar(64) NULL COMMENT '编码';
ALTER TABLE `sys_tenant` MODIFY COLUMN `tenant_domain` varchar(255) NULL COMMENT '文本内容';
ALTER TABLE `sys_tenant` MODIFY COLUMN `status` char(1) NULL DEFAULT '0' COMMENT '状态';
ALTER TABLE `sys_tenant` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_user
-- 表注释: 用户表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_user` MODIFY COLUMN `username` varchar(64) NULL COMMENT '名称';
ALTER TABLE `sys_user` MODIFY COLUMN `password` varchar(255) NULL COMMENT '密码';
ALTER TABLE `sys_user` MODIFY COLUMN `salt` varchar(255) NULL COMMENT '文本内容';
ALTER TABLE `sys_user` MODIFY COLUMN `phone` varchar(20) NULL COMMENT '电话号码';
ALTER TABLE `sys_user` MODIFY COLUMN `avatar` varchar(255) NULL COMMENT '头像';
ALTER TABLE `sys_user` MODIFY COLUMN `lock_flag` char(1) NULL DEFAULT '0' COMMENT '请补充注释';
ALTER TABLE `sys_user` MODIFY COLUMN `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志';

-- 数据库: mini
-- 表: sys_user_approval
-- 表注释: 用户授权第三方用户记录表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `sys_user_approval` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';

-- 数据库: mini
-- 表: undo_log
-- 表注释: seata分布式事务控制表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `undo_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `undo_log` MODIFY COLUMN `branch_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `undo_log` MODIFY COLUMN `xid` varchar(100) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `context` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `rollback_info` longblob(**********) NOT NULL COMMENT '请补充注释';
ALTER TABLE `undo_log` MODIFY COLUMN `log_status` int(10) NOT NULL COMMENT '状态';
ALTER TABLE `undo_log` MODIFY COLUMN `log_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `log_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `ext` varchar(100) NULL COMMENT '文本内容';

-- 数据库: mini
-- 表: user_app_info
-- 表注释: 商户App信息表

-- 补充缺失的字段注释
USE `mini`;

ALTER TABLE `user_app_info` MODIFY COLUMN `app_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `user_app_info` MODIFY COLUMN `app_key` varchar(128) NOT NULL COMMENT '键';
ALTER TABLE `user_app_info` MODIFY COLUMN `app_secret` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `user_app_info` MODIFY COLUMN `client_secret` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `user_app_info` MODIFY COLUMN `recharge_callback_url` varchar(128) NULL COMMENT '链接地址';
ALTER TABLE `user_app_info` MODIFY COLUMN `withdraw_callback_url` varchar(128) NULL COMMENT '链接地址';
ALTER TABLE `user_app_info` MODIFY COLUMN `nft_deploy_callback_url` varchar(128) NULL COMMENT '链接地址';
ALTER TABLE `user_app_info` MODIFY COLUMN `nft_mint_callback_url` varchar(128) NULL COMMENT '链接地址';
ALTER TABLE `user_app_info` MODIFY COLUMN `nft_recharge_callback_url` varchar(128) NULL COMMENT '链接地址';
ALTER TABLE `user_app_info` MODIFY COLUMN `nft_withdraw_callback_url` varchar(128) NULL COMMENT '链接地址';
ALTER TABLE `user_app_info` MODIFY COLUMN `expiration_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `user_app_info` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- ========================================
-- 数据库: mini_account
-- ========================================

-- 数据库: mini_account
-- 表: ac_nft_account
-- 表注释: NFT账户表

-- 补充缺失的字段注释
USE `mini_account`;

ALTER TABLE `ac_nft_account` MODIFY COLUMN `account_id` bigint(19) NULL COMMENT '数量';

-- 数据库: mini_account
-- 表: undo_log
-- 表注释: seata分布式事务控制表

-- 补充缺失的字段注释
USE `mini_account`;

ALTER TABLE `undo_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `undo_log` MODIFY COLUMN `branch_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `undo_log` MODIFY COLUMN `xid` varchar(100) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `context` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `rollback_info` longblob(**********) NOT NULL COMMENT '请补充注释';
ALTER TABLE `undo_log` MODIFY COLUMN `log_status` int(10) NOT NULL COMMENT '状态';
ALTER TABLE `undo_log` MODIFY COLUMN `log_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `log_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `ext` varchar(100) NULL COMMENT '文本内容';

-- ========================================
-- 数据库: mini_assets
-- ========================================

-- 数据库: mini_assets
-- 表: assets_account_change_log
-- 表注释: 账户变动日志

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_account_change_log` MODIFY COLUMN `account_id` bigint(19) NOT NULL COMMENT '数量';
ALTER TABLE `assets_account_change_log` MODIFY COLUMN `before_amount` decimal(30,16) NOT NULL COMMENT '金额';
ALTER TABLE `assets_account_change_log` MODIFY COLUMN `amount` decimal(30,16) NOT NULL COMMENT '金额';
ALTER TABLE `assets_account_change_log` MODIFY COLUMN `after_amount` decimal(30,16) NOT NULL COMMENT '金额';
ALTER TABLE `assets_account_change_log` MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';

-- 数据库: mini_assets
-- 表: assets_account_info
-- 表注释: 币种账户余额。商户配置币种时，生成商户账户信息。客户创建钱包同时生成账户信息。

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_account_info` MODIFY COLUMN `account_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `assets_account_info` MODIFY COLUMN `amount` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '金额';

-- 数据库: mini_assets
-- 表: assets_balance_frozen
-- 表注释: 记录客户/商户提现冻结的商户出入金热钱包

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_balance_frozen` MODIFY COLUMN `account_id` bigint(19) NOT NULL COMMENT '数量';
ALTER TABLE `assets_balance_frozen` MODIFY COLUMN `relation_type` int(10) NOT NULL COMMENT '类型';
ALTER TABLE `assets_balance_frozen` MODIFY COLUMN `unfreeze_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';

-- 数据库: mini_assets
-- 表: assets_chain_fee_record
-- 表注释: 链费转账记录

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_chain_fee_record` MODIFY COLUMN `record_id` bigint(19) NOT NULL COMMENT '主键ID';
ALTER TABLE `assets_chain_fee_record` MODIFY COLUMN `gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `assets_chain_fee_record` MODIFY COLUMN `gas_limit` decimal(36,18) NULL COMMENT '数值';

-- 数据库: mini_assets
-- 表: assets_collection_relation
-- 表注释: 一条归集记录，可对应多条充值记录。

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_collection_relation` MODIFY COLUMN `relation_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `assets_collection_relation` MODIFY COLUMN `relate_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `assets_collection_relation` MODIFY COLUMN `collection_record_id` bigint(19) NOT NULL COMMENT '数值';

-- 数据库: mini_assets
-- 表: assets_colletion_record
-- 表注释: 归集记录

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_colletion_record` MODIFY COLUMN `collection_record_id` bigint(19) NOT NULL COMMENT '主键ID';
ALTER TABLE `assets_colletion_record` MODIFY COLUMN `gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `assets_colletion_record` MODIFY COLUMN `gas_limit` decimal(36,18) NULL COMMENT '数值';

-- 数据库: mini_assets
-- 表: assets_deposit_record
-- 表注释: 记录业务相关的记录

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_deposit_record` MODIFY COLUMN `wallet_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `coin` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `from_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `to_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `amount` decimal(36,18) NOT NULL COMMENT '金额';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `txid` varchar(256) NOT NULL COMMENT '文本内容';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `confirm_num` int(10) NULL COMMENT '数量';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `confirm_time` timestamp NULL COMMENT '时间';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `tx_fee` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `assets_deposit_record` MODIFY COLUMN `callback_time` timestamp NULL COMMENT '时间';

-- 数据库: mini_assets
-- 表: assets_tenant_wallet_config
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`assets_tenant_wallet_config` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `tenant_coin_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `app_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `coin` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `coin_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `contract_addr` varchar(128) NULL COMMENT '文本内容';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `collection_waterline` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '数值';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `hot_wallet_toplimit` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '数值';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `hot_wallet_warn_limit` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '数值';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `gas_fee_warn_limit` decimal(36,18) NOT NULL COMMENT '数值';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `cold_wallet_toplimit` decimal(36,18) NULL DEFAULT '0.000000000000000000' COMMENT '数值';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `min_collection_amount` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '金额';
ALTER TABLE `assets_tenant_wallet_config` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: assets_transfer_step_log
-- 表注释: 交易步骤日志表

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_transfer_step_log` MODIFY COLUMN `his_step_id` bigint(19) NOT NULL COMMENT '主键ID';

-- 数据库: mini_assets
-- 表: assets_wallet_book_info
-- 表注释: 钱包子帐表

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_wallet_book_info` MODIFY COLUMN `wallet_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `assets_wallet_book_info` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `assets_wallet_book_info` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `assets_wallet_book_info` MODIFY COLUMN `amount` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '金额';
ALTER TABLE `assets_wallet_book_info` MODIFY COLUMN `update_user` varchar(64) NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: assets_wallet_change_log
-- 表注释: 商户钱包变动日志

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_wallet_change_log` MODIFY COLUMN `wallet_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `assets_wallet_change_log` MODIFY COLUMN `before_amount` decimal(36,18) NOT NULL COMMENT '金额';
ALTER TABLE `assets_wallet_change_log` MODIFY COLUMN `amount` decimal(36,18) NOT NULL COMMENT '金额';
ALTER TABLE `assets_wallet_change_log` MODIFY COLUMN `after_amount` decimal(36,18) NOT NULL COMMENT '金额';
ALTER TABLE `assets_wallet_change_log` MODIFY COLUMN `from_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `assets_wallet_change_log` MODIFY COLUMN `to_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `assets_wallet_change_log` MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';

-- 数据库: mini_assets
-- 表: assets_wallet_frozen
-- 表注释: 钱包资金冻结表

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_wallet_frozen` MODIFY COLUMN `relation_type` int(10) NOT NULL COMMENT '类型';
ALTER TABLE `assets_wallet_frozen` MODIFY COLUMN `amount` decimal(36,18) NOT NULL COMMENT '金额';
ALTER TABLE `assets_wallet_frozen` MODIFY COLUMN `unfreeze_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';

-- 数据库: mini_assets
-- 表: assets_wallet_information
-- 表注释: 钱包信息表

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_wallet_information` MODIFY COLUMN `wallet_info_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `assets_wallet_information` MODIFY COLUMN `app_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `assets_wallet_information` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `assets_wallet_information` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `assets_wallet_information` MODIFY COLUMN `wallet_address` varchar(256) NULL COMMENT '地址';
ALTER TABLE `assets_wallet_information` MODIFY COLUMN `wallet_name` varchar(256) NOT NULL COMMENT '名称';
ALTER TABLE `assets_wallet_information` MODIFY COLUMN `update_user` varchar(64) NULL COMMENT '文本内容';
ALTER TABLE `assets_wallet_information` MODIFY COLUMN `wallet_id` bigint(19) NULL COMMENT '数值';

-- 数据库: mini_assets
-- 表: assets_withdraw_apply
-- 表注释: 客户/商户提现申请表

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `apply_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `audit_time` timestamp NULL COMMENT '时间';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `txid` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `withdraw_time` timestamp NULL COMMENT '时间';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `callback_time` timestamp NULL COMMENT '时间';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `fee_speed` varchar(255) NOT NULL DEFAULT 'average' COMMENT '文本内容';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `gas_limit` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `nft_id` bigint(19) NULL COMMENT '数值';
ALTER TABLE `assets_withdraw_apply` MODIFY COLUMN `batch_send_time` timestamp NULL COMMENT '时间';

-- 数据库: mini_assets
-- 表: assets_withdraw_limit
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`assets_withdraw_limit` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `limit_id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `tenant_coin_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `min_withdraw_amount` decimal(36,18) NULL COMMENT '金额';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `max_withdraw_amount_once` decimal(36,18) NULL COMMENT '金额';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `max_withdraw_amount_day` decimal(36,18) NULL COMMENT '金额';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `day_start_time` time NULL COMMENT '时间';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `day_end_time` time NULL COMMENT '时间';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `day_once_audit_free` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `day_one_audit_num_limit` decimal(36,18) NULL COMMENT '数量';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `day_one_audit_amount_limit` decimal(36,18) NULL COMMENT '金额';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `day_all_audit_amount_limit` decimal(36,18) NULL COMMENT '金额';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `night_start_time` time NULL COMMENT '时间';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `night_end_time` time NULL COMMENT '时间';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `night_once_audit_free` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `night_one_audit_num_limit` decimal(36,18) NULL COMMENT '数量';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `night_one_audit_amount_limit` decimal(36,18) NULL COMMENT '金额';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `night_all_audit_amount_limit` decimal(36,18) NULL COMMENT '金额';
ALTER TABLE `assets_withdraw_limit` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: chain_coin_description
-- 表注释: 币种的充提币说明

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `chain_coin_description` MODIFY COLUMN `description_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `chain_coin_description` MODIFY COLUMN `tenant_coin_id` bigint(19) NULL COMMENT '数值';
ALTER TABLE `chain_coin_description` MODIFY COLUMN `coin_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `chain_coin_description` MODIFY COLUMN `content` text(65535) NULL COMMENT '文本内容';
ALTER TABLE `chain_coin_description` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: chain_coin_info
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`chain_coin_info` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `chain_coin_info` MODIFY COLUMN `coin_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `chain_coin_info` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_coin_info` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `chain_coin_info` MODIFY COLUMN `coin` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_coin_info` MODIFY COLUMN `coin_icon` varchar(128) NULL COMMENT '文本内容';
ALTER TABLE `chain_coin_info` MODIFY COLUMN `contract_addr` varchar(128) NULL COMMENT '文本内容';
ALTER TABLE `chain_coin_info` MODIFY COLUMN `gas_limit` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `chain_coin_info` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: chain_contract_template
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`chain_contract_template` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `chain_contract_template` MODIFY COLUMN `template_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `chain_contract_template` MODIFY COLUMN `protocol` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_contract_template` MODIFY COLUMN `contract_name` varchar(128) NOT NULL COMMENT '名称';
ALTER TABLE `chain_contract_template` MODIFY COLUMN `contract_class_name` varchar(128) NOT NULL COMMENT '名称';
ALTER TABLE `chain_contract_template` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: chain_info
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`chain_info` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `chain_info` MODIFY COLUMN `chain_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `chain_info` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_info` MODIFY COLUMN `base_coin` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_info` MODIFY COLUMN `safe_confirms` int(10) NOT NULL DEFAULT '6' COMMENT '数值';
ALTER TABLE `chain_info` MODIFY COLUMN `fast_confirms` int(10) NOT NULL DEFAULT '1' COMMENT '数值';
ALTER TABLE `chain_info` MODIFY COLUMN `gas_tracker_api` varchar(128) NOT NULL DEFAULT '' COMMENT '文本内容';
ALTER TABLE `chain_info` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_info` MODIFY COLUMN `low_gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `chain_info` MODIFY COLUMN `average_gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `chain_info` MODIFY COLUMN `high_gas_price` decimal(36,18) NULL COMMENT '价格';

-- 数据库: mini_assets
-- 表: chain_info_copy
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`chain_info_copy` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `chain_info_copy` MODIFY COLUMN `chain_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `base_coin` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `safe_confirms` int(10) NOT NULL DEFAULT '6' COMMENT '数值';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `fast_confirms` int(10) NOT NULL DEFAULT '1' COMMENT '数值';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `gas_tracker_api` varchar(128) NOT NULL DEFAULT '' COMMENT '文本内容';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `low_gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `average_gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `chain_info_copy` MODIFY COLUMN `high_gas_price` decimal(36,18) NULL COMMENT '价格';

-- 数据库: mini_assets
-- 表: chain_protocol_info
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`chain_protocol_info` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `chain_protocol_info` MODIFY COLUMN `protocol_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `chain_protocol_info` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_protocol_info` MODIFY COLUMN `protocol` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `chain_protocol_info` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: dms_wallet_address_modify_log
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_assets`.`dms_wallet_address_modify_log` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_wallet_info_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_wallet_info_id` bigint(19) NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_tenant_id` bigint(19) NOT NULL COMMENT '租户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_tenant_id` bigint(19) NULL COMMENT '租户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_app_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_app_id` bigint(19) NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_chain` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_wallet_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_wallet_address` varchar(256) NULL COMMENT '地址';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_wallet_type` int(10) NOT NULL COMMENT '类型';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_wallet_type` int(10) NULL COMMENT '类型';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_user_id` varchar(32) NOT NULL COMMENT '用户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_user_id` varchar(32) NULL COMMENT '用户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `create_by` varchar(128) NOT NULL COMMENT '创建人';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `create_time` datetime NOT NULL COMMENT '创建时间';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `update_by` varchar(128) NOT NULL COMMENT '更新人';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `update_time` datetime NOT NULL COMMENT '更新时间';

-- 数据库: mini_assets
-- 表: undo_log
-- 表注释: seata分布式事务控制表

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `undo_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `undo_log` MODIFY COLUMN `branch_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `undo_log` MODIFY COLUMN `xid` varchar(100) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `context` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `rollback_info` longblob(**********) NOT NULL COMMENT '请补充注释';
ALTER TABLE `undo_log` MODIFY COLUMN `log_status` int(10) NOT NULL COMMENT '状态';
ALTER TABLE `undo_log` MODIFY COLUMN `log_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `log_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `ext` varchar(100) NULL COMMENT '文本内容';

-- 数据库: mini_assets
-- 表: user_app_info
-- 表注释: 商户App信息表，只保留基本信息，用于关联使用名称查询，其他配置在mini库

-- 补充缺失的字段注释
USE `mini_assets`;

ALTER TABLE `user_app_info` MODIFY COLUMN `app_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `user_app_info` MODIFY COLUMN `update_user` varchar(64) NOT NULL COMMENT '文本内容';

-- ========================================
-- 数据库: mini_config
-- ========================================

-- 数据库: mini_config
-- 表: config_info
-- 表注释: config_info

-- 补充缺失的字段注释
USE `mini_config`;

ALTER TABLE `config_info` MODIFY COLUMN `group_id` varchar(255) NULL COMMENT '文本内容';
ALTER TABLE `config_info` MODIFY COLUMN `app_name` varchar(128) NULL COMMENT '名称';
ALTER TABLE `config_info` MODIFY COLUMN `c_desc` varchar(256) NULL COMMENT '描述';
ALTER TABLE `config_info` MODIFY COLUMN `c_use` varchar(64) NULL COMMENT '文本内容';
ALTER TABLE `config_info` MODIFY COLUMN `effect` varchar(64) NULL COMMENT '文本内容';
ALTER TABLE `config_info` MODIFY COLUMN `type` varchar(64) NULL COMMENT '类型';
ALTER TABLE `config_info` MODIFY COLUMN `c_schema` text(65535) NULL COMMENT '文本内容';

-- 数据库: mini_config
-- 表: config_info_aggr
-- 表注释: 增加租户字段

-- 补充缺失的字段注释
USE `mini_config`;

ALTER TABLE `config_info_aggr` MODIFY COLUMN `app_name` varchar(128) NULL COMMENT '名称';

-- 数据库: mini_config
-- 表: config_tags_relation
-- 表注释: config_tag_relation

-- 补充缺失的字段注释
USE `mini_config`;

ALTER TABLE `config_tags_relation` MODIFY COLUMN `nid` bigint(19) NOT NULL auto_increment COMMENT '主键ID';

-- 数据库: mini_config
-- 表: his_config_info
-- 表注释: 多租户改造

-- 补充缺失的字段注释
USE `mini_config`;

ALTER TABLE `his_config_info` MODIFY COLUMN `id` bigint(20) NOT NULL COMMENT '数值';
ALTER TABLE `his_config_info` MODIFY COLUMN `nid` bigint(20) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `his_config_info` MODIFY COLUMN `data_id` varchar(255) NOT NULL COMMENT '文本内容';
ALTER TABLE `his_config_info` MODIFY COLUMN `group_id` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `his_config_info` MODIFY COLUMN `content` longtext(**********) NOT NULL COMMENT '文本内容';
ALTER TABLE `his_config_info` MODIFY COLUMN `md5` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `his_config_info` MODIFY COLUMN `gmt_create` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '时间';
ALTER TABLE `his_config_info` MODIFY COLUMN `gmt_modified` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '时间';
ALTER TABLE `his_config_info` MODIFY COLUMN `src_user` text(65535) NULL COMMENT '文本内容';
ALTER TABLE `his_config_info` MODIFY COLUMN `src_ip` varchar(20) NULL COMMENT '文本内容';
ALTER TABLE `his_config_info` MODIFY COLUMN `op_type` char(10) NULL COMMENT '类型';

-- 数据库: mini_config
-- 表: permissions
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_config`.`permissions` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_config`;

ALTER TABLE `permissions` MODIFY COLUMN `role` varchar(50) NOT NULL COMMENT '文本内容';
ALTER TABLE `permissions` MODIFY COLUMN `resource` varchar(512) NOT NULL COMMENT '文本内容';
ALTER TABLE `permissions` MODIFY COLUMN `action` varchar(8) NOT NULL COMMENT '文本内容';

-- 数据库: mini_config
-- 表: roles
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_config`.`roles` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_config`;

ALTER TABLE `roles` MODIFY COLUMN `username` varchar(50) NOT NULL COMMENT '名称';
ALTER TABLE `roles` MODIFY COLUMN `role` varchar(50) NOT NULL COMMENT '文本内容';

-- 数据库: mini_config
-- 表: users
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_config`.`users` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_config`;

ALTER TABLE `users` MODIFY COLUMN `username` varchar(50) NOT NULL COMMENT '名称';
ALTER TABLE `users` MODIFY COLUMN `password` varchar(500) NOT NULL COMMENT '密码';
ALTER TABLE `users` MODIFY COLUMN `enabled` tinyint(3) NOT NULL COMMENT '数值';

-- ========================================
-- 数据库: mini_dms
-- ========================================

-- 数据库: mini_dms
-- 表: dms_wallet_address_modify_log
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_dms`.`dms_wallet_address_modify_log` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_dms`;

ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_wallet_info_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_wallet_info_id` bigint(19) NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_tenant_id` bigint(19) NOT NULL COMMENT '租户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_tenant_id` bigint(19) NULL COMMENT '租户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_app_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_app_id` bigint(19) NULL COMMENT '数值';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_chain` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_wallet_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_wallet_address` varchar(256) NULL COMMENT '地址';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_wallet_type` int(10) NOT NULL COMMENT '类型';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_wallet_type` int(10) NULL COMMENT '类型';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `old_user_id` varchar(32) NOT NULL COMMENT '用户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `new_user_id` varchar(32) NULL COMMENT '用户ID';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `create_by` varchar(128) NOT NULL COMMENT '创建人';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `create_time` datetime NOT NULL COMMENT '创建时间';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `update_by` varchar(128) NOT NULL COMMENT '更新人';
ALTER TABLE `dms_wallet_address_modify_log` MODIFY COLUMN `update_time` datetime NOT NULL COMMENT '更新时间';

-- ========================================
-- 数据库: mini_guess
-- ========================================

-- 数据库: mini_guess
-- 表: undo_log
-- 表注释: seata分布式事务控制表

-- 补充缺失的字段注释
USE `mini_guess`;

ALTER TABLE `undo_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `undo_log` MODIFY COLUMN `branch_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `undo_log` MODIFY COLUMN `xid` varchar(100) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `context` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `rollback_info` longblob(**********) NOT NULL COMMENT '请补充注释';
ALTER TABLE `undo_log` MODIFY COLUMN `log_status` int(10) NOT NULL COMMENT '状态';
ALTER TABLE `undo_log` MODIFY COLUMN `log_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `log_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `ext` varchar(100) NULL COMMENT '文本内容';

-- ========================================
-- 数据库: mini_job
-- ========================================

-- 数据库: mini_job
-- 表: qrtz_blob_triggers
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_blob_triggers` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_blob_triggers` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_blob_triggers` MODIFY COLUMN `trigger_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_blob_triggers` MODIFY COLUMN `trigger_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_blob_triggers` MODIFY COLUMN `blob_data` blob(65535) NULL COMMENT '请补充注释';

-- 数据库: mini_job
-- 表: qrtz_calendars
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_calendars` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_calendars` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_calendars` MODIFY COLUMN `calendar_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_calendars` MODIFY COLUMN `calendar` blob(65535) NOT NULL COMMENT '请补充注释';

-- 数据库: mini_job
-- 表: qrtz_cron_triggers
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_cron_triggers` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_cron_triggers` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_cron_triggers` MODIFY COLUMN `trigger_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_cron_triggers` MODIFY COLUMN `trigger_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_cron_triggers` MODIFY COLUMN `cron_expression` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_cron_triggers` MODIFY COLUMN `time_zone_id` varchar(80) NULL COMMENT '文本内容';

-- 数据库: mini_job
-- 表: qrtz_fired_triggers
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_fired_triggers` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `entry_id` varchar(95) NOT NULL COMMENT '主键ID';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `trigger_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `trigger_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `instance_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `fired_time` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `sched_time` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `priority` int(10) NOT NULL COMMENT '数值';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `state` varchar(16) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `job_name` varchar(200) NULL COMMENT '名称';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `job_group` varchar(200) NULL COMMENT '文本内容';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `is_nonconcurrent` varchar(1) NULL COMMENT '文本内容';
ALTER TABLE `qrtz_fired_triggers` MODIFY COLUMN `requests_recovery` varchar(1) NULL COMMENT '文本内容';

-- 数据库: mini_job
-- 表: qrtz_job_details
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_job_details` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_job_details` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `job_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `job_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `description` varchar(250) NULL COMMENT '描述';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `job_class_name` varchar(250) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `is_durable` varchar(1) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `is_nonconcurrent` varchar(1) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `is_update_data` varchar(1) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `requests_recovery` varchar(1) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_job_details` MODIFY COLUMN `job_data` blob(65535) NULL COMMENT '请补充注释';

-- 数据库: mini_job
-- 表: qrtz_locks
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_locks` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_locks` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_locks` MODIFY COLUMN `lock_name` varchar(40) NOT NULL COMMENT '名称';

-- 数据库: mini_job
-- 表: qrtz_paused_trigger_grps
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_paused_trigger_grps` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_paused_trigger_grps` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_paused_trigger_grps` MODIFY COLUMN `trigger_group` varchar(200) NOT NULL COMMENT '文本内容';

-- 数据库: mini_job
-- 表: qrtz_scheduler_state
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_scheduler_state` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_scheduler_state` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_scheduler_state` MODIFY COLUMN `instance_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_scheduler_state` MODIFY COLUMN `last_checkin_time` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `qrtz_scheduler_state` MODIFY COLUMN `checkin_interval` bigint(19) NOT NULL COMMENT '数值';

-- 数据库: mini_job
-- 表: qrtz_simple_triggers
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_simple_triggers` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_simple_triggers` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_simple_triggers` MODIFY COLUMN `trigger_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_simple_triggers` MODIFY COLUMN `trigger_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_simple_triggers` MODIFY COLUMN `repeat_count` bigint(19) NOT NULL COMMENT '数量';
ALTER TABLE `qrtz_simple_triggers` MODIFY COLUMN `repeat_interval` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `qrtz_simple_triggers` MODIFY COLUMN `times_triggered` bigint(19) NOT NULL COMMENT '数值';

-- 数据库: mini_job
-- 表: qrtz_simprop_triggers
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_simprop_triggers` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `trigger_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `trigger_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `str_prop_1` varchar(512) NULL COMMENT '文本内容';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `str_prop_2` varchar(512) NULL COMMENT '文本内容';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `str_prop_3` varchar(512) NULL COMMENT '文本内容';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `int_prop_1` int(10) NULL COMMENT '数值';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `int_prop_2` int(10) NULL COMMENT '数值';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `long_prop_1` bigint(19) NULL COMMENT '数值';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `long_prop_2` bigint(19) NULL COMMENT '数值';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `dec_prop_1` decimal(13,4) NULL COMMENT '数值';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `dec_prop_2` decimal(13,4) NULL COMMENT '数值';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `bool_prop_1` varchar(1) NULL COMMENT '文本内容';
ALTER TABLE `qrtz_simprop_triggers` MODIFY COLUMN `bool_prop_2` varchar(1) NULL COMMENT '文本内容';

-- 数据库: mini_job
-- 表: qrtz_triggers
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`qrtz_triggers` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `qrtz_triggers` MODIFY COLUMN `sched_name` varchar(120) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `trigger_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `trigger_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `job_name` varchar(200) NOT NULL COMMENT '名称';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `job_group` varchar(200) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `description` varchar(250) NULL COMMENT '描述';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `next_fire_time` bigint(19) NULL COMMENT '数值';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `prev_fire_time` bigint(19) NULL COMMENT '数值';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `priority` int(10) NULL COMMENT '数值';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `trigger_state` varchar(16) NOT NULL COMMENT '文本内容';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `trigger_type` varchar(8) NOT NULL COMMENT '类型';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `start_time` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `end_time` bigint(19) NULL COMMENT '数值';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `calendar_name` varchar(200) NULL COMMENT '名称';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `misfire_instr` smallint(5) NULL COMMENT '数值';
ALTER TABLE `qrtz_triggers` MODIFY COLUMN `job_data` blob(65535) NULL COMMENT '请补充注释';

-- 数据库: mini_job
-- 表: xxl_job_group
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`xxl_job_group` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `xxl_job_group` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `xxl_job_group` MODIFY COLUMN `update_time` datetime NULL COMMENT '更新时间';

-- 数据库: mini_job
-- 表: xxl_job_info
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`xxl_job_info` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `xxl_job_info` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `xxl_job_info` MODIFY COLUMN `job_desc` varchar(255) NOT NULL COMMENT '描述';
ALTER TABLE `xxl_job_info` MODIFY COLUMN `add_time` datetime NULL COMMENT '时间';
ALTER TABLE `xxl_job_info` MODIFY COLUMN `update_time` datetime NULL COMMENT '更新时间';

-- 数据库: mini_job
-- 表: xxl_job_log
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`xxl_job_log` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `xxl_job_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';

-- 数据库: mini_job
-- 表: xxl_job_log_report
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`xxl_job_log_report` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `xxl_job_log_report` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `xxl_job_log_report` MODIFY COLUMN `update_time` datetime NULL COMMENT '更新时间';

-- 数据库: mini_job
-- 表: xxl_job_logglue
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`xxl_job_logglue` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `xxl_job_logglue` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `xxl_job_logglue` MODIFY COLUMN `add_time` datetime NULL COMMENT '时间';
ALTER TABLE `xxl_job_logglue` MODIFY COLUMN `update_time` datetime NULL COMMENT '更新时间';

-- 数据库: mini_job
-- 表: xxl_job_registry
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`xxl_job_registry` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `xxl_job_registry` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `xxl_job_registry` MODIFY COLUMN `registry_group` varchar(50) NOT NULL COMMENT '文本内容';
ALTER TABLE `xxl_job_registry` MODIFY COLUMN `registry_key` varchar(255) NOT NULL COMMENT '键';
ALTER TABLE `xxl_job_registry` MODIFY COLUMN `registry_value` varchar(255) NOT NULL COMMENT '值';
ALTER TABLE `xxl_job_registry` MODIFY COLUMN `update_time` datetime NULL COMMENT '更新时间';

-- 数据库: mini_job
-- 表: xxl_job_user
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_job`.`xxl_job_user` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_job`;

ALTER TABLE `xxl_job_user` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';

-- ========================================
-- 数据库: mini_market
-- ========================================

-- 数据库: mini_market
-- 表: tb_coin_app_market
-- 表注释: 设置不同应用、服务返回的币对

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `tb_coin_app_market` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';

-- 数据库: mini_market
-- 表: tb_coin_market_off_market
-- 表注释: 开休市管理

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `tb_coin_market_off_market` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `tb_coin_market_off_market` MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE `tb_coin_market_off_market` MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';

-- 数据库: mini_market
-- 表: tb_coin_market_theme
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_market`.`tb_coin_market_theme` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `tb_coin_market_theme` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `tb_coin_market_theme` MODIFY COLUMN `sort` int(10) NULL COMMENT '排序';
ALTER TABLE `tb_coin_market_theme` MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE `tb_coin_market_theme` MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';

-- 数据库: mini_market
-- 表: tb_coin_turnover
-- 表注释: 成交量浮动表

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `tb_coin_turnover` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `tb_coin_turnover` MODIFY COLUMN `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';
ALTER TABLE `tb_coin_turnover` MODIFY COLUMN `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '更新时间';

-- 数据库: mini_market
-- 表: tb_off_market_template
-- 表注释: 开休市模板名称

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `tb_off_market_template` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `tb_off_market_template` MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '更新时间';
ALTER TABLE `tb_off_market_template` MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';

-- 数据库: mini_market
-- 表: tb_off_market_template_detail
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_market`.`tb_off_market_template_detail` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `tb_off_market_template_detail` MODIFY COLUMN `id` int(10) NULL DEFAULT '0' COMMENT '数值';
ALTER TABLE `tb_off_market_template_detail` MODIFY COLUMN `template_id` int(10) NULL COMMENT '数值';
ALTER TABLE `tb_off_market_template_detail` MODIFY COLUMN `open_time` timestamp NULL COMMENT '时间';
ALTER TABLE `tb_off_market_template_detail` MODIFY COLUMN `close_time` timestamp NULL COMMENT '时间';
ALTER TABLE `tb_off_market_template_detail` MODIFY COLUMN `update_time` timestamp NULL COMMENT '更新时间';
ALTER TABLE `tb_off_market_template_detail` MODIFY COLUMN `create_time` timestamp NULL COMMENT '创建时间';

-- 数据库: mini_market
-- 表: tb_push_topic_info
-- 表注释: websocket topic 信息表

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `tb_push_topic_info` MODIFY COLUMN `id` int(10) NOT NULL auto_increment COMMENT '主键ID';

-- 数据库: mini_market
-- 表: undo_log
-- 表注释: seata分布式事务控制表

-- 补充缺失的字段注释
USE `mini_market`;

ALTER TABLE `undo_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `undo_log` MODIFY COLUMN `branch_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `undo_log` MODIFY COLUMN `xid` varchar(100) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `context` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `rollback_info` longblob(**********) NOT NULL COMMENT '请补充注释';
ALTER TABLE `undo_log` MODIFY COLUMN `log_status` int(10) NOT NULL COMMENT '状态';
ALTER TABLE `undo_log` MODIFY COLUMN `log_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `log_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `ext` varchar(100) NULL COMMENT '文本内容';

-- ========================================
-- 数据库: mini_user
-- ========================================

-- 数据库: mini_user
-- 表: undo_log
-- 表注释: seata分布式事务控制表

-- 补充缺失的字段注释
USE `mini_user`;

ALTER TABLE `undo_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `undo_log` MODIFY COLUMN `branch_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `undo_log` MODIFY COLUMN `xid` varchar(100) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `context` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `rollback_info` longblob(**********) NOT NULL COMMENT '请补充注释';
ALTER TABLE `undo_log` MODIFY COLUMN `log_status` int(10) NOT NULL COMMENT '状态';
ALTER TABLE `undo_log` MODIFY COLUMN `log_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `log_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `ext` varchar(100) NULL COMMENT '文本内容';

-- 数据库: mini_user
-- 表: user_activity_switch_cfg
-- 表注释: 营销活动类型总开关配置

-- 补充缺失的字段注释
USE `mini_user`;

ALTER TABLE `user_activity_switch_cfg` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';

-- 数据库: mini_user
-- 表: user_login_info
-- 表注释: 用户登录信息表

-- 补充缺失的字段注释
USE `mini_user`;

ALTER TABLE `user_login_info` MODIFY COLUMN `last_login_ip` varchar(64) NULL COMMENT '文本内容';
ALTER TABLE `user_login_info` MODIFY COLUMN `last_login_terminal` varchar(64) NULL COMMENT '文本内容';

-- 数据库: mini_user
-- 表: user_operation_log
-- 表注释: 用户操作日志表

-- 补充缺失的字段注释
USE `mini_user`;

ALTER TABLE `user_operation_log` MODIFY COLUMN `device_id` varchar(64) NULL COMMENT '文本内容';

-- 数据库: mini_user
-- 表: user_quick_link_cfg
-- 表注释: 金刚区配置

-- 补充缺失的字段注释
USE `mini_user`;

ALTER TABLE `user_quick_link_cfg` MODIFY COLUMN `unsupported_versions` varchar(512) NULL COMMENT '版本';

-- 数据库: mini_user
-- 表: user_sms_record
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_user`.`user_sms_record` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_user`;

ALTER TABLE `user_sms_record` MODIFY COLUMN `id` bigint(19) NOT NULL COMMENT '主键ID';

-- ========================================
-- 数据库: mini_wallet
-- ========================================

-- 数据库: mini_wallet
-- 表: undo_log
-- 表注释: seata分布式事务控制表

-- 补充缺失的字段注释
USE `mini_wallet`;

ALTER TABLE `undo_log` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `undo_log` MODIFY COLUMN `branch_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `undo_log` MODIFY COLUMN `xid` varchar(100) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `context` varchar(128) NOT NULL COMMENT '文本内容';
ALTER TABLE `undo_log` MODIFY COLUMN `rollback_info` longblob(**********) NOT NULL COMMENT '请补充注释';
ALTER TABLE `undo_log` MODIFY COLUMN `log_status` int(10) NOT NULL COMMENT '状态';
ALTER TABLE `undo_log` MODIFY COLUMN `log_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `log_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '时间';
ALTER TABLE `undo_log` MODIFY COLUMN `ext` varchar(100) NULL COMMENT '文本内容';

-- 数据库: mini_wallet
-- 表: wallet_block_scanner
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_wallet`.`wallet_block_scanner` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_wallet`;

ALTER TABLE `wallet_block_scanner` MODIFY COLUMN `id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `wallet_block_scanner` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `wallet_block_scanner` MODIFY COLUMN `block_hash` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `wallet_block_scanner` MODIFY COLUMN `block_height` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `wallet_block_scanner` MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED COMMENT '创建时间';
ALTER TABLE `wallet_block_scanner` MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP DEFAULT_GENERATED on update CURRENT_TIMESTAMP COMMENT '更新时间';

-- 数据库: mini_wallet
-- 表: wallet_in_record
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_wallet`.`wallet_in_record` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_wallet`;

ALTER TABLE `wallet_in_record` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `from_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `to_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `amount` decimal(36,18) NOT NULL COMMENT '金额';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `txid` varchar(256) NOT NULL COMMENT '文本内容';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `confirm_num` int(10) NULL COMMENT '数量';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `confirm_time` timestamp NULL COMMENT '时间';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `tx_fee` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `wallet_in_record` MODIFY COLUMN `log_index` varchar(32) NULL COMMENT '文本内容';

-- 数据库: mini_wallet
-- 表: wallet_info
-- 表注释: 钱包地址信息

-- 补充缺失的字段注释
USE `mini_wallet`;

ALTER TABLE `wallet_info` MODIFY COLUMN `wallet_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `wallet_info` MODIFY COLUMN `tenant_id` bigint(19) NOT NULL DEFAULT '0' COMMENT '租户ID';
ALTER TABLE `wallet_info` MODIFY COLUMN `app_id` bigint(19) NOT NULL COMMENT '数值';
ALTER TABLE `wallet_info` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `wallet_info` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `wallet_info` MODIFY COLUMN `coin` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `wallet_info` MODIFY COLUMN `wallet_address` varchar(256) NOT NULL COMMENT '地址';

-- 数据库: mini_wallet
-- 表: wallet_nft_deploy_info
-- 表注释: NFT合约deploy信息

-- 补充缺失的字段注释
USE `mini_wallet`;

ALTER TABLE `wallet_nft_deploy_info` MODIFY COLUMN `confirm_num` int(10) NULL COMMENT '数量';
ALTER TABLE `wallet_nft_deploy_info` MODIFY COLUMN `confirm_time` timestamp NULL COMMENT '时间';
ALTER TABLE `wallet_nft_deploy_info` MODIFY COLUMN `tx_fee` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `wallet_nft_deploy_info` MODIFY COLUMN `txid` varchar(256) NOT NULL COMMENT '文本内容';
ALTER TABLE `wallet_nft_deploy_info` MODIFY COLUMN `gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `wallet_nft_deploy_info` MODIFY COLUMN `gas_limit` decimal(36,18) NULL COMMENT '数值';

-- 数据库: mini_wallet
-- 表: wallet_nft_mint_info
-- 表注释: NFTmint表

-- 补充缺失的字段注释
USE `mini_wallet`;

ALTER TABLE `wallet_nft_mint_info` MODIFY COLUMN `mint_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `wallet_nft_mint_info` MODIFY COLUMN `confirm_num` int(10) NULL COMMENT '数量';
ALTER TABLE `wallet_nft_mint_info` MODIFY COLUMN `confirm_time` timestamp NULL COMMENT '时间';
ALTER TABLE `wallet_nft_mint_info` MODIFY COLUMN `tx_fee` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `wallet_nft_mint_info` MODIFY COLUMN `txid` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `wallet_nft_mint_info` MODIFY COLUMN `gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `wallet_nft_mint_info` MODIFY COLUMN `gas_limit` decimal(36,18) NULL COMMENT '数值';

-- 数据库: mini_wallet
-- 表: wallet_out_record
-- ⚠️ 表缺少注释，建议添加表注释
-- ALTER TABLE `mini_wallet`.`wallet_out_record` COMMENT = '请补充表注释';

-- 补充缺失的字段注释
USE `mini_wallet`;

ALTER TABLE `wallet_out_record` MODIFY COLUMN `record_id` bigint(19) NOT NULL auto_increment COMMENT '主键ID';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `chain` varchar(32) NOT NULL COMMENT '文本内容';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `protocol` varchar(32) NULL COMMENT '文本内容';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `from_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `to_address` varchar(256) NOT NULL COMMENT '地址';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `amount` decimal(36,18) NOT NULL COMMENT '金额';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `txid` varchar(256) NULL COMMENT '文本内容';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `confirm_num` int(10) NULL COMMENT '数量';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `confirm_time` timestamp NULL COMMENT '时间';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `tx_fee` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `gas_price` decimal(36,18) NULL COMMENT '价格';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `gas_limit` decimal(36,18) NULL COMMENT '数值';
ALTER TABLE `wallet_out_record` MODIFY COLUMN `log_index` varchar(32) NULL COMMENT '文本内容';

