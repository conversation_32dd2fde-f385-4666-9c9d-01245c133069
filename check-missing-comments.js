const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库连接配置
const dbConfig = {
  host: '*************',
  port: 3306,
  user: 'root',
  password: 'Nod%D*h2y2*oUZuz',
  charset: 'utf8mb4'
};

// 创建输出目录
const outputDir = './database-analysis';

// 获取所有数据库
async function getAllDatabases(connection) {
  try {
    const [rows] = await connection.query('SHOW DATABASES');
    const systemDbs = ['information_schema', 'performance_schema', 'mysql', 'sys'];
    const databases = rows
      .map(row => row.Database)
      .filter(db => !systemDbs.includes(db) && !db.toLowerCase().includes('mock'));
    
    return databases;
  } catch (error) {
    console.error('获取数据库列表失败:', error);
    return [];
  }
}

// 获取数据库中的所有表
async function getTablesInDatabase(connection, database) {
  try {
    await connection.query(`USE \`${database}\``);
    const [rows] = await connection.query('SHOW TABLES');
    const tableKey = `Tables_in_${database}`;
    return rows.map(row => row[tableKey]);
  } catch (error) {
    console.error(`获取数据库 ${database} 的表列表失败:`, error);
    return [];
  }
}

// 检查缺少注释的字段
async function checkMissingComments(connection, database, tableName) {
  try {
    const [columns] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT,
        COLUMN_KEY,
        EXTRA
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [database, tableName]);

    // 获取表注释
    const [tableInfo] = await connection.execute(`
      SELECT TABLE_COMMENT 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    `, [database, tableName]);

    const missingComments = columns.filter(col => 
      !col.COLUMN_COMMENT || col.COLUMN_COMMENT.trim() === '' || col.COLUMN_COMMENT === '无注释'
    );

    return {
      database,
      tableName,
      tableComment: tableInfo[0]?.TABLE_COMMENT || '',
      totalColumns: columns.length,
      missingComments: missingComments.length,
      missingCommentFields: missingComments.map(col => ({
        columnName: col.COLUMN_NAME,
        dataType: col.DATA_TYPE,
        columnKey: col.COLUMN_KEY,
        extra: col.EXTRA
      }))
    };
  } catch (error) {
    console.error(`检查表 ${database}.${tableName} 注释失败:`, error);
    return null;
  }
}

// 生成缺少注释的报告
function generateMissingCommentsReport(missingCommentsData) {
  let report = `# 数据库字段注释缺失报告\n\n`;
  report += `**生成时间**: ${new Date().toLocaleString()}\n\n`;
  
  let totalTables = 0;
  let totalTablesWithMissingComments = 0;
  let totalMissingFields = 0;
  
  const databaseSummary = {};
  
  missingCommentsData.forEach(item => {
    if (!item) return;
    
    totalTables++;
    if (item.missingComments > 0) {
      totalTablesWithMissingComments++;
      totalMissingFields += item.missingComments;
    }
    
    if (!databaseSummary[item.database]) {
      databaseSummary[item.database] = {
        tables: 0,
        tablesWithMissing: 0,
        missingFields: 0
      };
    }
    
    databaseSummary[item.database].tables++;
    if (item.missingComments > 0) {
      databaseSummary[item.database].tablesWithMissing++;
      databaseSummary[item.database].missingFields += item.missingComments;
    }
  });
  
  report += `## 总体统计\n\n`;
  report += `- **总表数**: ${totalTables}\n`;
  report += `- **有缺失注释的表数**: ${totalTablesWithMissingComments}\n`;
  report += `- **缺失注释的字段总数**: ${totalMissingFields}\n\n`;
  
  report += `## 各数据库统计\n\n`;
  report += `| 数据库 | 总表数 | 有缺失注释的表数 | 缺失注释字段数 |\n`;
  report += `|--------|--------|------------------|----------------|\n`;
  
  Object.keys(databaseSummary).forEach(db => {
    const summary = databaseSummary[db];
    report += `| ${db} | ${summary.tables} | ${summary.tablesWithMissing} | ${summary.missingFields} |\n`;
  });
  
  report += `\n## 详细信息\n\n`;
  
  const groupedByDatabase = {};
  missingCommentsData.forEach(item => {
    if (!item || item.missingComments === 0) return;
    
    if (!groupedByDatabase[item.database]) {
      groupedByDatabase[item.database] = [];
    }
    groupedByDatabase[item.database].push(item);
  });
  
  Object.keys(groupedByDatabase).forEach(database => {
    report += `### 数据库: ${database}\n\n`;
    
    groupedByDatabase[database].forEach(item => {
      report += `#### 表: ${item.tableName}\n`;
      if (item.tableComment) {
        report += `**表注释**: ${item.tableComment}\n`;
      } else {
        report += `**表注释**: ⚠️ 缺少表注释\n`;
      }
      report += `**缺失注释字段数**: ${item.missingComments}/${item.totalColumns}\n\n`;
      
      if (item.missingCommentFields.length > 0) {
        report += `**缺失注释的字段**:\n\n`;
        report += `| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |\n`;
        report += `|--------|----------|--------|----------|----------|\n`;
        
        item.missingCommentFields.forEach(field => {
          const suggestedComment = generateSuggestedComment(field.columnName, field.dataType, field.columnKey);
          report += `| ${field.columnName} | ${field.dataType} | ${field.columnKey || ''} | ${field.extra || ''} | ${suggestedComment} |\n`;
        });
        report += `\n`;
      }
      
      report += `---\n\n`;
    });
  });
  
  return report;
}

// 根据字段名和类型生成建议的注释
function generateSuggestedComment(columnName, dataType, columnKey) {
  const name = columnName.toLowerCase();
  
  // 主键
  if (columnKey === 'PRI' && (name.includes('id') || name === 'id')) {
    return '主键ID';
  }
  
  // 常见字段模式
  const patterns = {
    'create_time': '创建时间',
    'update_time': '更新时间',
    'create_by': '创建人',
    'update_by': '更新人',
    'del_flag': '删除标志',
    'status': '状态',
    'type': '类型',
    'name': '名称',
    'code': '编码',
    'sort': '排序',
    'order': '排序',
    'remark': '备注',
    'desc': '描述',
    'description': '描述',
    'phone': '电话号码',
    'email': '邮箱',
    'address': '地址',
    'url': '链接地址',
    'path': '路径',
    'file': '文件',
    'image': '图片',
    'pic': '图片',
    'avatar': '头像',
    'password': '密码',
    'token': '令牌',
    'key': '键',
    'value': '值',
    'amount': '金额',
    'price': '价格',
    'count': '数量',
    'num': '数量',
    'level': '等级',
    'grade': '等级',
    'version': '版本'
  };
  
  // 检查字段名是否包含常见模式
  for (const [pattern, comment] of Object.entries(patterns)) {
    if (name.includes(pattern)) {
      return comment;
    }
  }
  
  // 根据数据类型推测
  if (dataType.includes('time') || dataType.includes('date')) {
    return '时间';
  }
  
  if (dataType.includes('text') || dataType.includes('varchar')) {
    return '文本内容';
  }
  
  if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float')) {
    return '数值';
  }
  
  return '请补充注释';
}

// 主函数
async function checkMissingCommentsMain() {
  let connection;
  
  try {
    console.log('开始连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功!');
    
    const databases = await getAllDatabases(connection);
    console.log(`发现数据库: ${databases.join(', ')}`);
    
    const allMissingCommentsData = [];
    
    for (const database of databases) {
      console.log(`\n检查数据库: ${database}`);
      
      const tables = await getTablesInDatabase(connection, database);
      console.log(`发现 ${tables.length} 个表`);
      
      for (const tableName of tables) {
        console.log(`  检查表: ${tableName}`);
        const missingData = await checkMissingComments(connection, database, tableName);
        if (missingData) {
          allMissingCommentsData.push(missingData);
          if (missingData.missingComments > 0) {
            console.log(`    ⚠️  发现 ${missingData.missingComments} 个字段缺少注释`);
          }
        }
      }
    }
    
    // 生成报告
    const report = generateMissingCommentsReport(allMissingCommentsData);
    const reportPath = path.join(outputDir, 'missing-comments-report.md');
    await fs.writeFile(reportPath, report, 'utf8');
    console.log(`\n缺失注释报告已生成: ${reportPath}`);
    
    console.log('\n检查完成!');
    
  } catch (error) {
    console.error('检查过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行检查
if (require.main === module) {
  checkMissingCommentsMain().catch(console.error);
}

module.exports = { checkMissingCommentsMain };
