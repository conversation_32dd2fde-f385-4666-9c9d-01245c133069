const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库连接配置
const dbConfig = {
  host: '*************',
  port: 3306,
  user: 'root',
  password: 'Nod%D*h2y2*oUZuz',
  charset: 'utf8mb4'
};

// 创建输出目录
const outputDir = './database-analysis';

async function createOutputDirectory() {
  try {
    await fs.mkdir(outputDir, { recursive: true });
    console.log(`输出目录已创建: ${outputDir}`);
  } catch (error) {
    console.error('创建输出目录失败:', error);
  }
}

// 获取所有数据库
async function getAllDatabases(connection) {
  try {
    const [rows] = await connection.query('SHOW DATABASES');
    // 过滤掉系统数据库和mock库
    const systemDbs = ['information_schema', 'performance_schema', 'mysql', 'sys'];
    const databases = rows
      .map(row => row.Database)
      .filter(db => !systemDbs.includes(db) && !db.toLowerCase().includes('mock'));

    console.log(`发现数据库: ${databases.join(', ')}`);
    return databases;
  } catch (error) {
    console.error('获取数据库列表失败:', error);
    return [];
  }
}

// 获取数据库中的所有表
async function getTablesInDatabase(connection, database) {
  try {
    await connection.query(`USE \`${database}\``);
    const [rows] = await connection.query('SHOW TABLES');
    const tableKey = `Tables_in_${database}`;
    return rows.map(row => row[tableKey]);
  } catch (error) {
    console.error(`获取数据库 ${database} 的表列表失败:`, error);
    return [];
  }
}

// 获取表结构信息
async function getTableStructure(connection, database, tableName) {
  try {
    // 获取表的列信息
    const [columns] = await connection.execute(`
      SELECT
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT,
        COLUMN_KEY,
        EXTRA,
        CHARACTER_MAXIMUM_LENGTH,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [database, tableName]);

    // 获取表注释
    const [tableInfo] = await connection.execute(`
      SELECT TABLE_COMMENT
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    `, [database, tableName]);

    // 获取索引信息
    const [indexes] = await connection.execute(`
      SELECT
        INDEX_NAME,
        COLUMN_NAME,
        NON_UNIQUE,
        INDEX_TYPE
      FROM INFORMATION_SCHEMA.STATISTICS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY INDEX_NAME, SEQ_IN_INDEX
    `, [database, tableName]);

    return {
      tableName,
      tableComment: tableInfo[0]?.TABLE_COMMENT || '',
      columns,
      indexes
    };
  } catch (error) {
    console.error(`获取表 ${database}.${tableName} 结构失败:`, error);
    return null;
  }
}

// 生成表结构的Markdown文档
function generateTableMarkdown(tableStructure) {
  const { tableName, tableComment, columns, indexes } = tableStructure;
  
  let markdown = `## 表名: ${tableName}\n\n`;
  
  if (tableComment) {
    markdown += `**表注释**: ${tableComment}\n\n`;
  }
  
  // 字段信息
  markdown += `### 字段信息\n\n`;
  markdown += `| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |\n`;
  markdown += `|--------|----------|----------|--------|----------|--------|----------|\n`;
  
  columns.forEach(column => {
    const dataType = column.CHARACTER_MAXIMUM_LENGTH 
      ? `${column.DATA_TYPE}(${column.CHARACTER_MAXIMUM_LENGTH})`
      : column.NUMERIC_PRECISION 
        ? `${column.DATA_TYPE}(${column.NUMERIC_PRECISION}${column.NUMERIC_SCALE ? ',' + column.NUMERIC_SCALE : ''})`
        : column.DATA_TYPE;
    
    const nullable = column.IS_NULLABLE === 'YES' ? '是' : '否';
    const defaultValue = column.COLUMN_DEFAULT || '';
    const comment = column.COLUMN_COMMENT || '无注释';
    const keyType = column.COLUMN_KEY || '';
    const extra = column.EXTRA || '';
    
    markdown += `| ${column.COLUMN_NAME} | ${dataType} | ${nullable} | ${defaultValue} | ${comment} | ${keyType} | ${extra} |\n`;
  });
  
  // 索引信息
  if (indexes.length > 0) {
    markdown += `\n### 索引信息\n\n`;
    markdown += `| 索引名 | 字段名 | 是否唯一 | 索引类型 |\n`;
    markdown += `|--------|--------|----------|----------|\n`;
    
    indexes.forEach(index => {
      const unique = index.NON_UNIQUE === 0 ? '是' : '否';
      markdown += `| ${index.INDEX_NAME} | ${index.COLUMN_NAME} | ${unique} | ${index.INDEX_TYPE} |\n`;
    });
  }
  
  markdown += `\n---\n\n`;
  return markdown;
}

// 主函数
async function scanDatabase() {
  let connection;
  
  try {
    console.log('开始连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功!');
    
    // 创建输出目录
    await createOutputDirectory();
    
    // 获取所有数据库
    const databases = await getAllDatabases(connection);
    
    if (databases.length === 0) {
      console.log('没有找到可分析的数据库');
      return;
    }
    
    // 为每个数据库创建分析文档
    for (const database of databases) {
      console.log(`\n正在分析数据库: ${database}`);
      
      const tables = await getTablesInDatabase(connection, database);
      console.log(`发现 ${tables.length} 个表`);
      
      if (tables.length === 0) {
        continue;
      }
      
      let databaseMarkdown = `# 数据库: ${database}\n\n`;
      databaseMarkdown += `**分析时间**: ${new Date().toLocaleString()}\n\n`;
      databaseMarkdown += `**表数量**: ${tables.length}\n\n`;
      databaseMarkdown += `**表列表**: ${tables.join(', ')}\n\n`;
      databaseMarkdown += `---\n\n`;
      
      // 分析每个表
      for (const tableName of tables) {
        console.log(`  分析表: ${tableName}`);
        const tableStructure = await getTableStructure(connection, database, tableName);
        
        if (tableStructure) {
          databaseMarkdown += generateTableMarkdown(tableStructure);
        }
      }
      
      // 保存数据库分析文档
      const filename = `${database}_analysis.md`;
      const filepath = path.join(outputDir, filename);
      await fs.writeFile(filepath, databaseMarkdown, 'utf8');
      console.log(`数据库 ${database} 分析完成，保存到: ${filepath}`);
    }
    
    // 生成总览文档
    await generateSummaryDocument(databases);
    
    console.log('\n数据库扫描分析完成!');
    
  } catch (error) {
    console.error('数据库扫描过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 生成总览文档
async function generateSummaryDocument(databases) {
  let summary = `# 数据库分析总览\n\n`;
  summary += `**分析时间**: ${new Date().toLocaleString()}\n\n`;
  summary += `**数据库服务器**: ${dbConfig.host}:${dbConfig.port}\n\n`;
  summary += `**分析的数据库数量**: ${databases.length}\n\n`;
  
  summary += `## 数据库列表\n\n`;
  databases.forEach((db, index) => {
    summary += `${index + 1}. **${db}** - [查看详细分析](${db}_analysis.md)\n`;
  });
  
  summary += `\n## 使用说明\n\n`;
  summary += `- 每个数据库的详细分析保存在对应的 \`{数据库名}_analysis.md\` 文件中\n`;
  summary += `- 表结构包含字段信息、数据类型、注释、索引等详细信息\n`;
  summary += `- 如果字段缺少注释，建议在数据库中补充相应的注释信息\n`;
  
  const summaryPath = path.join(outputDir, 'README.md');
  await fs.writeFile(summaryPath, summary, 'utf8');
  console.log(`总览文档已生成: ${summaryPath}`);
}

// 运行扫描
if (require.main === module) {
  scanDatabase().catch(console.error);
}

module.exports = { scanDatabase };
