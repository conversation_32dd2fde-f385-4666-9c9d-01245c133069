# 数据库: mini

**分析时间**: 2025/7/24 18:11:23

**表数量**: 27

**表列表**: sys_area_info, sys_client_app, sys_copywriting_cfg, sys_dept, sys_dept_relation, sys_dict, sys_dict_item, sys_email_first_platform_cfg, sys_file, sys_language_text_cfg, sys_log, sys_menu, sys_oauth_client_details, sys_post, sys_public_param, sys_role, sys_role_menu, sys_route_conf, sys_social_details, sys_tenant, sys_user, sys_user_approval, sys_user_post, sys_user_role, sys_weight_cfg, undo_log, user_app_info

---

## 表名: sys_area_info

**表注释**: 地区信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| area_code | varchar(32) | 否 |  | 地区编码 |  |  |
| area_name_text_key | varchar(64) | 否 |  | 地区名称 |  |  |
| area_full_name_text_key | varchar(64) | 否 |  | 地区全称 |  |  |
| area_type | tinyint(3) | 否 |  | 地区类型（0-国家/中国港澳台地区；1-省、直辖市、特别行政区；2-地级市；3-区县） |  |  |
| affiliation_area_code | varchar(32) | 是 |  | 所属地区编码 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_client_app

**表注释**: 授权应用的关联信息

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| client_id | varchar(64) | 否 |  | 授权客户端id |  |  |
| bundle_id | varchar(128) | 否 |  | 应用在应用商城的唯一id |  |  |
| url_scheme | varchar(255) | 是 |  | 跳转路径 |  |  |
| device_type | varchar(32) | 否 |  | ios  android 设备类型 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_copywriting_cfg

**表注释**: 文案配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增主键 | PRI |  |
| copywriting_type | varchar(64) | 否 |  | 文案类型 |  |  |
| copywriting_text_key | varchar(64) | 否 |  | 文案文本key |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_dept

**表注释**: 部门管理

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| dept_id | bigint(19) | 否 |  | 无注释 | PRI |  |
| name | varchar(50) | 是 |  | 无注释 |  |  |
| sort_order | int(10) | 否 | 0 | 排序 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 |  | 创建时间 |  |  |
| update_time | datetime | 是 |  | 修改时间 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| parent_id | bigint(19) | 是 |  | 无注释 |  |  |
| tenant_id | bigint(19) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | dept_id | 是 | BTREE |

---

## 表名: sys_dept_relation

**表注释**: 部门关系表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| ancestor | bigint(19) | 否 |  | 祖先节点 | PRI |  |
| descendant | bigint(19) | 否 |  | 后代节点 | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx1 | ancestor | 否 | BTREE |
| idx2 | descendant | 否 | BTREE |
| PRIMARY | ancestor | 是 | BTREE |
| PRIMARY | descendant | 是 | BTREE |

---

## 表名: sys_dict

**表注释**: 字典表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 编号 | PRI |  |
| dict_type | varchar(100) | 是 |  | 无注释 |  |  |
| description | varchar(100) | 是 |  | 无注释 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 |  | 创建时间 |  |  |
| update_time | datetime | 是 |  | 更新时间 |  |  |
| remarks | varchar(255) | 是 |  | 无注释 |  |  |
| system_flag | char(1) | 是 | 0 | 无注释 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 | MUL |  |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| sys_dict_del_flag | del_flag | 否 | BTREE |

---

## 表名: sys_dict_item

**表注释**: 字典项

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 编号 | PRI |  |
| dict_id | bigint(19) | 否 |  | 无注释 |  |  |
| item_value | varchar(100) | 是 |  | 无注释 | MUL |  |
| label | varchar(100) | 是 |  | 无注释 | MUL |  |
| dict_type | varchar(100) | 是 |  | 无注释 |  |  |
| description | varchar(100) | 是 |  | 无注释 |  |  |
| sort_order | int(10) | 否 | 0 | 排序（升序） |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 |  | 创建时间 |  |  |
| update_time | datetime | 是 |  | 更新时间 |  |  |
| remarks | varchar(255) | 是 |  | 无注释 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 | MUL |  |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| sys_dict_item_del_flag | del_flag | 否 | BTREE |
| sys_dict_label | label | 否 | BTREE |
| sys_dict_value | item_value | 否 | BTREE |

---

## 表名: sys_email_first_platform_cfg

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| email_suffix | varchar(64) | 否 |  | 邮箱后缀 |  |  |
| email_platform | varchar(32) | 否 |  | 邮件平台 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_file

**表注释**: 文件管理表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 编号 | PRI |  |
| file_name | varchar(100) | 是 |  | 无注释 |  |  |
| bucket_name | varchar(200) | 是 |  | 无注释 |  |  |
| original | varchar(100) | 是 |  | 无注释 |  |  |
| type | varchar(50) | 是 |  | 无注释 |  |  |
| file_size | bigint(19) | 是 |  | 文件大小 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 |  | 上传时间 |  |  |
| update_time | datetime | 是 |  | 更新时间 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| tenant_id | bigint(19) | 是 |  | 所属租户 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_language_text_cfg

**表注释**: 语言文本配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| language_key | varchar(64) | 否 |  | 语言key | MUL |  |
| language_type | varchar(32) | 否 |  | 语言类型：LanguageEnum
（
zh_cn：简体中文；
zh_hk：繁体中文；
en_us：英文
） | MUL |  |
| language_text | text(65535) | 否 |  | 语言文本 |  |  |
| business_describe | varchar(512) | 是 |  | 业务描述 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_language_key | language_key | 否 | BTREE |
| idx_language_type | language_type | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_log

**表注释**: 日志表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 编号 | PRI |  |
| log_type | char(1) | 是 | 0 | 无注释 | MUL |  |
| title | varchar(255) | 是 |  | 无注释 |  |  |
| service_id | varchar(32) | 是 |  | 无注释 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 | MUL |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |
| remote_addr | varchar(255) | 是 |  | 无注释 |  |  |
| user_agent | varchar(1000) | 是 |  | 无注释 |  |  |
| request_uri | varchar(255) | 是 |  | 无注释 | MUL |  |
| method | varchar(10) | 是 |  | 无注释 |  |  |
| params | text(65535) | 是 |  | 无注释 |  |  |
| time | bigint(19) | 是 |  | 执行时间 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| exception | text(65535) | 是 |  | 无注释 |  |  |
| tenant_id | bigint(19) | 是 | 0 | 所属租户 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| sys_log_create_date | create_time | 否 | BTREE |
| sys_log_request_uri | request_uri | 否 | BTREE |
| sys_log_type | log_type | 否 | BTREE |

---

## 表名: sys_menu

**表注释**: 菜单权限表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| menu_id | bigint(19) | 否 |  | 菜单ID | PRI |  |
| name | varchar(128) | 是 |  | 无注释 |  |  |
| permission | varchar(32) | 是 |  | 无注释 |  |  |
| path | varchar(128) | 是 |  | 无注释 |  |  |
| parent_id | bigint(19) | 是 |  | 父菜单ID |  |  |
| icon | varchar(32) | 是 |  | 无注释 |  |  |
| sort_order | int(10) | 是 | 1 | 排序值 |  |  |
| keep_alive | char(1) | 是 | 0 | 无注释 |  |  |
| menu_type | char(1) | 是 | 0 | 无注释 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| create_time | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| update_time | datetime | 是 |  | 更新时间 |  | on update CURRENT_TIMESTAMP |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| tenant_id | bigint(20) | 是 |  | 租户ID |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | menu_id | 是 | BTREE |

---

## 表名: sys_oauth_client_details

**表注释**: 终端信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | ID | PRI |  |
| client_id | varchar(32) | 否 |  | 无注释 |  |  |
| resource_ids | varchar(256) | 是 |  | 无注释 |  |  |
| client_secret | varchar(256) | 是 |  | 无注释 |  |  |
| scope | varchar(256) | 是 |  | 无注释 |  |  |
| authorized_grant_types | varchar(256) | 是 |  | 无注释 |  |  |
| web_server_redirect_uri | varchar(256) | 是 |  | 无注释 |  |  |
| authorities | varchar(256) | 是 |  | 无注释 |  |  |
| access_token_validity | int(10) | 是 |  | 无注释 |  |  |
| refresh_token_validity | int(10) | 是 |  | 无注释 |  |  |
| additional_information | varchar(4096) | 是 |  | 无注释 |  |  |
| autoapprove | varchar(256) | 是 |  | 无注释 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 |  | 上传时间 |  |  |
| update_time | datetime | 是 |  | 更新时间 |  |  |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_post

**表注释**: 岗位信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| post_id | bigint(19) | 否 |  | 岗位ID | PRI |  |
| post_code | varchar(64) | 否 |  | 岗位编码 |  |  |
| post_name | varchar(50) | 否 |  | 岗位名称 |  |  |
| post_sort | int(10) | 否 |  | 岗位排序 |  |  |
| remark | varchar(500) | 是 |  | 岗位描述 |  |  |
| del_flag | char(1) | 否 | 0 | 是否删除  -1：已删除  0：正常 |  |  |
| create_time | datetime | 是 |  | 创建时间 |  |  |
| create_by | varchar(64) | 否 |  | 创建人 |  |  |
| update_time | datetime | 是 |  | 更新时间 |  |  |
| update_by | varchar(64) | 否 |  | 更新人 |  |  |
| tenant_id | bigint(19) | 是 |  | 租户ID |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | post_id | 是 | BTREE |

---

## 表名: sys_public_param

**表注释**: 公共参数配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| public_id | bigint(19) | 否 |  | 编号 | PRI |  |
| public_name | varchar(128) | 是 |  | 无注释 |  |  |
| public_key | varchar(128) | 是 |  | 无注释 |  |  |
| public_value | varchar(128) | 是 |  | 无注释 |  |  |
| status | char(1) | 是 | 0 | 无注释 |  |  |
| validate_code | varchar(64) | 是 |  | 无注释 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | datetime | 是 |  | 修改时间 |  | on update CURRENT_TIMESTAMP |
| public_type | char(1) | 是 | 0 | 无注释 |  |  |
| system_flag | char(1) | 是 | 0 | 无注释 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| tenant_id | bigint(19) | 是 |  | 租户ID |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | public_id | 是 | BTREE |

---

## 表名: sys_role

**表注释**: 系统角色表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| role_id | bigint(19) | 否 |  | 无注释 | PRI |  |
| role_name | varchar(64) | 是 |  | 无注释 |  |  |
| role_code | varchar(64) | 是 |  | 无注释 | MUL |  |
| role_desc | varchar(255) | 是 |  | 无注释 |  |  |
| ds_type | char(1) | 是 | 2 | 无注释 |  |  |
| ds_scope | varchar(255) | 是 |  | 无注释 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| tenant_id | bigint(19) | 是 |  | 无注释 |  |  |
| create_time | datetime | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| update_time | datetime | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | role_id | 是 | BTREE |
| role_idx1_role_code | role_code | 否 | BTREE |

---

## 表名: sys_role_menu

**表注释**: 角色菜单表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| role_id | bigint(19) | 否 |  | 角色ID | PRI |  |
| menu_id | bigint(19) | 否 |  | 菜单ID | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | role_id | 是 | BTREE |
| PRIMARY | menu_id | 是 | BTREE |

---

## 表名: sys_route_conf

**表注释**: 路由配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| route_name | varchar(30) | 是 |  | 无注释 |  |  |
| route_id | varchar(30) | 是 |  | 无注释 |  |  |
| predicates | json | 是 |  | 断言 |  |  |
| filters | json | 是 |  | 过滤器 |  |  |
| uri | varchar(50) | 是 |  | 无注释 |  |  |
| sort_order | int(10) | 是 | 0 | 排序 |  |  |
| metadata | json | 是 |  | 路由元信息 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | datetime | 是 |  | 修改时间 |  | on update CURRENT_TIMESTAMP |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_social_details

**表注释**: 系统社交登录账号表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主鍵 | PRI |  |
| type | varchar(16) | 是 |  | 无注释 |  |  |
| remark | varchar(64) | 是 |  | 无注释 |  |  |
| app_id | varchar(64) | 是 |  | 无注释 |  |  |
| app_secret | varchar(64) | 是 |  | 无注释 |  |  |
| redirect_url | varchar(128) | 是 |  | 无注释 |  |  |
| ext | varchar(255) | 是 |  | 拓展字段 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | datetime | 是 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_tenant

**表注释**: 租户表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 租户id | PRI |  |
| name | varchar(255) | 是 |  | 无注释 |  |  |
| code | varchar(64) | 是 |  | 无注释 |  |  |
| tenant_domain | varchar(255) | 是 |  | 无注释 |  |  |
| start_time | datetime | 是 |  | 开始时间 |  |  |
| end_time | datetime | 是 |  | 结束时间 |  |  |
| status | char(1) | 是 | 0 | 无注释 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_time | datetime | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: sys_user

**表注释**: 用户表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| user_id | bigint(19) | 否 |  | 主键ID | PRI |  |
| username | varchar(64) | 是 |  | 无注释 | MUL |  |
| password | varchar(255) | 是 |  | 无注释 |  |  |
| salt | varchar(255) | 是 |  | 无注释 |  |  |
| phone | varchar(20) | 是 |  | 无注释 |  |  |
| avatar | varchar(255) | 是 |  | 无注释 |  |  |
| nickname | varchar(64) | 是 |  | 拓展字段:昵称 |  |  |
| name | varchar(64) | 是 |  | 拓展字段:姓名 |  |  |
| email | varchar(128) | 是 |  | 拓展字段:邮箱 |  |  |
| dept_id | bigint(19) | 是 |  | 部门ID |  |  |
| create_by | varchar(64) | 否 |   | 创建人 |  |  |
| update_by | varchar(64) | 否 |   | 修改人 |  |  |
| create_time | datetime | 是 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | datetime | 是 |  | 修改时间 |  | on update CURRENT_TIMESTAMP |
| lock_flag | char(1) | 是 | 0 | 无注释 |  |  |
| del_flag | char(1) | 是 | 0 | 无注释 |  |  |
| wx_openid | varchar(32) | 是 |  | 微信登录openId | MUL |  |
| mini_openid | varchar(32) | 是 |  | 小程序openId |  |  |
| qq_openid | varchar(32) | 是 |  | QQ openId | MUL |  |
| gitee_login | varchar(100) | 是 |  | 码云 标识 |  |  |
| osc_id | varchar(100) | 是 |  | 开源中国 标识 |  |  |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |
| google_verify_key | varchar(64) | 是 |  | 谷歌校验密钥 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | user_id | 是 | BTREE |
| user_idx1_username | username | 否 | BTREE |
| user_qq_openid | qq_openid | 否 | BTREE |
| user_wx_openid | wx_openid | 否 | BTREE |

---

## 表名: sys_user_approval

**表注释**: 用户授权第三方用户记录表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| uid | bigint(19) | 是 |  | 授权用户uid | MUL |  |
| client_id | varchar(64) | 是 |  | 第三方应用id |  |  |
| scope | varchar(255) | 是 |  | 已授权范围 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uid_index | uid | 否 | BTREE |
| uid_index | client_id | 否 | BTREE |

---

## 表名: sys_user_post

**表注释**: 用户与岗位关联表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| user_id | bigint(19) | 否 |  | 用户ID | PRI |  |
| post_id | bigint(19) | 否 |  | 岗位ID | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | user_id | 是 | BTREE |
| PRIMARY | post_id | 是 | BTREE |

---

## 表名: sys_user_role

**表注释**: 用户角色表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| user_id | bigint(19) | 否 |  | 用户ID | PRI |  |
| role_id | bigint(19) | 否 |  | 角色ID | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | user_id | 是 | BTREE |
| PRIMARY | role_id | 是 | BTREE |

---

## 表名: sys_weight_cfg

**表注释**: 权重配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| server | varchar(64) | 否 |  | 权重名 |  |  |
| type | varchar(64) | 否 |  | 权重类型 |  |  |
| weight | int(10) | 否 |  | 权重值 |  |  |
| enable_switch | tinyint(3) | 否 | 1 | 启用开关（0-关；1-开） |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: undo_log

**表注释**: seata分布式事务控制表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| branch_id | bigint(19) | 否 |  | 无注释 |  |  |
| xid | varchar(100) | 否 |  | 无注释 | MUL |  |
| context | varchar(128) | 否 |  | 无注释 |  |  |
| rollback_info | longblob(4294967295) | 否 |  | 无注释 |  |  |
| log_status | int(10) | 否 |  | 无注释 |  |  |
| log_created | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| log_modified | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| ext | varchar(100) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| ux_undo_log | xid | 是 | BTREE |
| ux_undo_log | branch_id | 是 | BTREE |

---

## 表名: user_app_info

**表注释**: 商户App信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| app_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| tenant_id | bigint(19) | 否 | 0 | 所属租户 |  |  |
| app_name | varchar(128) | 否 |  | App名称 |  |  |
| app_icon | varchar(128) | 是 |  | AppIcon |  |  |
| app_key | varchar(128) | 否 |  | 无注释 |  |  |
| app_secret | varchar(128) | 否 |  | 无注释 |  |  |
| client_secret | varchar(128) | 否 |  | 无注释 |  |  |
| recharge_callback_url | varchar(128) | 是 |  | 无注释 |  |  |
| withdraw_callback_url | varchar(128) | 是 |  | 无注释 |  |  |
| nft_deploy_callback_url | varchar(128) | 是 |  | 无注释 |  |  |
| nft_mint_callback_url | varchar(128) | 是 |  | 无注释 |  |  |
| nft_recharge_callback_url | varchar(128) | 是 |  | 无注释 |  |  |
| nft_withdraw_callback_url | varchar(128) | 是 |  | 无注释 |  |  |
| is_nft_collection | int(10) | 否 | 0 | 0-不归集（默认）；1-归集 |  |  |
| is_audit | int(10) | 否 | 1 | 客户提现是否需要审核:0-不审核；1-审核 |  |  |
| expiration_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| status | int(10) | 否 | 0 | 0-无效；1-有效 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| update_user | varchar(64) | 否 |  | 无注释 |  |  |
| ip_white | varchar(255) | 否 |  | 允许通过的IP |  |  |
| resource_ids | varchar(256) | 是 |  | 接入资源列表 |  |  |
| scope | varchar(256) | 是 | waas-open-api | 指定客户端的权限范围(read,write,trust)可指定多个用逗号隔开 |  |  |
| authorized_grant_types | varchar(256) | 是 | client_credentials | 指定客户端支持的grant_type |  |  |
| web_server_redirect_uri | varchar(256) | 是 |  | 指定客户端的重定向URL |  |  |
| authorities | varchar(256) | 是 |  | 指定客户端所拥有的Spring Security权限值,可指定多个用逗号隔开 |  |  |
| access_token_validity | int(10) | 是 | 7200 | 指定客户端access_token的有效时间值,单位秒 |  |  |
| refresh_token_validity | int(10) | 是 | 259200 | 指定客户端refresh_token的有效时间值,单位秒 |  |  |
| additional_information | varchar(4096) | 是 |  | 预留字段,在OAuth2流程中没有实际使用 |  |  |
| autoapprove | varchar(256) | 是 | false | 设置用户是否自动Approve操作,默认为false |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | app_id | 是 | BTREE |

---

