# 数据库字段注释缺失报告

**生成时间**: 2025/7/24 18:13:55

## 总体统计

- **总表数**: 168
- **有缺失注释的表数**: 91
- **缺失注释的字段总数**: 547

## 各数据库统计

| 数据库 | 总表数 | 有缺失注释的表数 | 缺失注释字段数 |
|--------|--------|------------------|----------------|
| mini | 27 | 17 | 114 |
| mini_account | 15 | 2 | 10 |
| mini_assets | 27 | 24 | 168 |
| mini_config | 12 | 7 | 28 |
| mini_dms | 6 | 1 | 18 |
| mini_guess | 14 | 1 | 9 |
| mini_job | 21 | 18 | 97 |
| mini_market | 11 | 8 | 30 |
| mini_user | 28 | 6 | 15 |
| mini_wallet | 7 | 7 | 58 |

## 详细信息

### 数据库: mini

#### 表: sys_client_app
**表注释**: 授权应用的关联信息
**缺失注释字段数**: 1/5

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |

---

#### 表: sys_dept
**表注释**: 部门管理
**缺失注释字段数**: 5/10

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| dept_id | bigint | PRI |  | 主键ID |
| name | varchar |  |  | 名称 |
| del_flag | char |  |  | 删除标志 |
| parent_id | bigint |  |  | 数值 |
| tenant_id | bigint |  |  | 数值 |

---

#### 表: sys_dict
**表注释**: 字典表
**缺失注释字段数**: 5/11

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| dict_type | varchar |  |  | 类型 |
| description | varchar |  |  | 描述 |
| remarks | varchar |  |  | 备注 |
| system_flag | char |  |  | 请补充注释 |
| del_flag | char | MUL |  | 删除标志 |

---

#### 表: sys_dict_item
**表注释**: 字典项
**缺失注释字段数**: 7/14

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| dict_id | bigint |  |  | 数值 |
| item_value | varchar | MUL |  | 值 |
| label | varchar | MUL |  | 文本内容 |
| dict_type | varchar |  |  | 类型 |
| description | varchar |  |  | 描述 |
| remarks | varchar |  |  | 备注 |
| del_flag | char | MUL |  | 删除标志 |

---

#### 表: sys_file
**表注释**: 文件管理表
**缺失注释字段数**: 5/12

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| file_name | varchar |  |  | 名称 |
| bucket_name | varchar |  |  | 名称 |
| original | varchar |  |  | 文本内容 |
| type | varchar |  |  | 类型 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_log
**表注释**: 日志表
**缺失注释字段数**: 10/17

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| log_type | char | MUL |  | 类型 |
| title | varchar |  |  | 文本内容 |
| service_id | varchar |  |  | 文本内容 |
| remote_addr | varchar |  |  | 文本内容 |
| user_agent | varchar |  |  | 文本内容 |
| request_uri | varchar | MUL |  | 文本内容 |
| method | varchar |  |  | 文本内容 |
| params | text |  |  | 文本内容 |
| del_flag | char |  |  | 删除标志 |
| exception | text |  |  | 文本内容 |

---

#### 表: sys_menu
**表注释**: 菜单权限表
**缺失注释字段数**: 7/15

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| name | varchar |  |  | 名称 |
| permission | varchar |  |  | 文本内容 |
| path | varchar |  |  | 路径 |
| icon | varchar |  |  | 文本内容 |
| keep_alive | char |  |  | 请补充注释 |
| menu_type | char |  |  | 类型 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_oauth_client_details
**表注释**: 终端信息表
**缺失注释字段数**: 12/18

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| client_id | varchar |  |  | 文本内容 |
| resource_ids | varchar |  |  | 文本内容 |
| client_secret | varchar |  |  | 文本内容 |
| scope | varchar |  |  | 文本内容 |
| authorized_grant_types | varchar |  |  | 类型 |
| web_server_redirect_uri | varchar |  |  | 文本内容 |
| authorities | varchar |  |  | 文本内容 |
| access_token_validity | int |  |  | 令牌 |
| refresh_token_validity | int |  |  | 令牌 |
| additional_information | varchar |  |  | 文本内容 |
| autoapprove | varchar |  |  | 文本内容 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_public_param
**表注释**: 公共参数配置表
**缺失注释字段数**: 8/14

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| public_name | varchar |  |  | 名称 |
| public_key | varchar |  |  | 键 |
| public_value | varchar |  |  | 值 |
| status | char |  |  | 状态 |
| validate_code | varchar |  |  | 编码 |
| public_type | char |  |  | 类型 |
| system_flag | char |  |  | 请补充注释 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_role
**表注释**: 系统角色表
**缺失注释字段数**: 10/12

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| role_id | bigint | PRI |  | 主键ID |
| role_name | varchar |  |  | 名称 |
| role_code | varchar | MUL |  | 编码 |
| role_desc | varchar |  |  | 描述 |
| ds_type | char |  |  | 类型 |
| ds_scope | varchar |  |  | 文本内容 |
| del_flag | char |  |  | 删除标志 |
| tenant_id | bigint |  |  | 数值 |
| create_time | datetime |  | DEFAULT_GENERATED | 创建时间 |
| update_time | datetime |  |  | 更新时间 |

---

#### 表: sys_route_conf
**表注释**: 路由配置表
**缺失注释字段数**: 4/13

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| route_name | varchar |  |  | 名称 |
| route_id | varchar |  |  | 文本内容 |
| uri | varchar |  |  | 文本内容 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_social_details
**表注释**: 系统社交登录账号表
**缺失注释字段数**: 6/13

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| type | varchar |  |  | 类型 |
| remark | varchar |  |  | 备注 |
| app_id | varchar |  |  | 文本内容 |
| app_secret | varchar |  |  | 文本内容 |
| redirect_url | varchar |  |  | 链接地址 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_tenant
**表注释**: 租户表
**缺失注释字段数**: 5/12

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| name | varchar |  |  | 名称 |
| code | varchar |  |  | 编码 |
| tenant_domain | varchar |  |  | 文本内容 |
| status | char |  |  | 状态 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_user
**表注释**: 用户表
**缺失注释字段数**: 7/23

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| username | varchar | MUL |  | 名称 |
| password | varchar |  |  | 密码 |
| salt | varchar |  |  | 文本内容 |
| phone | varchar |  |  | 电话号码 |
| avatar | varchar |  |  | 头像 |
| lock_flag | char |  |  | 请补充注释 |
| del_flag | char |  |  | 删除标志 |

---

#### 表: sys_user_approval
**表注释**: 用户授权第三方用户记录表
**缺失注释字段数**: 1/4

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |

---

#### 表: undo_log
**表注释**: seata分布式事务控制表
**缺失注释字段数**: 9/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| branch_id | bigint |  |  | 数值 |
| xid | varchar | MUL |  | 文本内容 |
| context | varchar |  |  | 文本内容 |
| rollback_info | longblob |  |  | 请补充注释 |
| log_status | int |  |  | 状态 |
| log_created | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| log_modified | timestamp |  | DEFAULT_GENERATED | 时间 |
| ext | varchar |  |  | 文本内容 |

---

#### 表: user_app_info
**表注释**: 商户App信息表
**缺失注释字段数**: 12/30

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| app_id | bigint | PRI | auto_increment | 主键ID |
| app_key | varchar |  |  | 键 |
| app_secret | varchar |  |  | 文本内容 |
| client_secret | varchar |  |  | 文本内容 |
| recharge_callback_url | varchar |  |  | 链接地址 |
| withdraw_callback_url | varchar |  |  | 链接地址 |
| nft_deploy_callback_url | varchar |  |  | 链接地址 |
| nft_mint_callback_url | varchar |  |  | 链接地址 |
| nft_recharge_callback_url | varchar |  |  | 链接地址 |
| nft_withdraw_callback_url | varchar |  |  | 链接地址 |
| expiration_time | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| update_user | varchar |  |  | 文本内容 |

---

### 数据库: mini_account

#### 表: ac_nft_account
**表注释**: NFT账户表
**缺失注释字段数**: 1/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| account_id | bigint |  |  | 数量 |

---

#### 表: undo_log
**表注释**: seata分布式事务控制表
**缺失注释字段数**: 9/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| branch_id | bigint |  |  | 数值 |
| xid | varchar | MUL |  | 文本内容 |
| context | varchar |  |  | 文本内容 |
| rollback_info | longblob |  |  | 请补充注释 |
| log_status | int |  |  | 状态 |
| log_created | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| log_modified | timestamp |  | DEFAULT_GENERATED | 时间 |
| ext | varchar |  |  | 文本内容 |

---

### 数据库: mini_assets

#### 表: assets_account_change_log
**表注释**: 账户变动日志
**缺失注释字段数**: 5/11

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| account_id | bigint |  |  | 数量 |
| before_amount | decimal |  |  | 金额 |
| amount | decimal |  |  | 金额 |
| after_amount | decimal |  |  | 金额 |
| create_time | timestamp |  | DEFAULT_GENERATED | 创建时间 |

---

#### 表: assets_account_info
**表注释**: 币种账户余额。商户配置币种时，生成商户账户信息。客户创建钱包同时生成账户信息。
**缺失注释字段数**: 2/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| account_id | bigint | PRI | auto_increment | 主键ID |
| amount | decimal |  |  | 金额 |

---

#### 表: assets_balance_frozen
**表注释**: 记录客户/商户提现冻结的商户出入金热钱包
**缺失注释字段数**: 3/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| account_id | bigint |  |  | 数量 |
| relation_type | int |  |  | 类型 |
| unfreeze_time | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |

---

#### 表: assets_chain_fee_record
**表注释**: 链费转账记录
**缺失注释字段数**: 3/20

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| record_id | bigint | PRI |  | 主键ID |
| gas_price | decimal |  |  | 价格 |
| gas_limit | decimal |  |  | 数值 |

---

#### 表: assets_collection_relation
**表注释**: 一条归集记录，可对应多条充值记录。
**缺失注释字段数**: 3/6

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| relation_id | bigint | PRI | auto_increment | 主键ID |
| relate_id | bigint |  |  | 数值 |
| collection_record_id | bigint |  |  | 数值 |

---

#### 表: assets_colletion_record
**表注释**: 归集记录
**缺失注释字段数**: 3/21

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| collection_record_id | bigint | PRI |  | 主键ID |
| gas_price | decimal |  |  | 价格 |
| gas_limit | decimal |  |  | 数值 |

---

#### 表: assets_deposit_record
**表注释**: 记录业务相关的记录
**缺失注释字段数**: 12/30

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| wallet_id | bigint |  |  | 数值 |
| chain | varchar |  |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| coin | varchar |  |  | 文本内容 |
| from_address | varchar |  |  | 地址 |
| to_address | varchar |  |  | 地址 |
| amount | decimal |  |  | 金额 |
| txid | varchar |  |  | 文本内容 |
| confirm_num | int |  |  | 数量 |
| confirm_time | timestamp |  |  | 时间 |
| tx_fee | decimal |  |  | 数值 |
| callback_time | timestamp |  |  | 时间 |

---

#### 表: assets_tenant_wallet_config
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 14/22

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| tenant_coin_id | bigint | PRI | auto_increment | 主键ID |
| app_id | bigint |  |  | 数值 |
| chain | varchar |  |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| coin | varchar |  |  | 文本内容 |
| coin_id | bigint |  |  | 数值 |
| contract_addr | varchar |  |  | 文本内容 |
| collection_waterline | decimal |  |  | 数值 |
| hot_wallet_toplimit | decimal |  |  | 数值 |
| hot_wallet_warn_limit | decimal |  |  | 数值 |
| gas_fee_warn_limit | decimal |  |  | 数值 |
| cold_wallet_toplimit | decimal |  |  | 数值 |
| min_collection_amount | decimal |  |  | 金额 |
| update_user | varchar |  |  | 文本内容 |

---

#### 表: assets_transfer_step_log
**表注释**: 交易步骤日志表
**缺失注释字段数**: 1/6

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| his_step_id | bigint | PRI |  | 主键ID |

---

#### 表: assets_wallet_book_info
**表注释**: 钱包子帐表
**缺失注释字段数**: 5/13

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| wallet_id | bigint | PRI | auto_increment | 主键ID |
| chain | varchar |  |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| amount | decimal |  |  | 金额 |
| update_user | varchar |  |  | 文本内容 |

---

#### 表: assets_wallet_change_log
**表注释**: 商户钱包变动日志
**缺失注释字段数**: 7/14

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| wallet_id | bigint |  |  | 数值 |
| before_amount | decimal |  |  | 金额 |
| amount | decimal |  |  | 金额 |
| after_amount | decimal |  |  | 金额 |
| from_address | varchar |  |  | 地址 |
| to_address | varchar |  |  | 地址 |
| create_time | timestamp |  | DEFAULT_GENERATED | 创建时间 |

---

#### 表: assets_wallet_frozen
**表注释**: 钱包资金冻结表
**缺失注释字段数**: 3/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| relation_type | int |  |  | 类型 |
| amount | decimal |  |  | 金额 |
| unfreeze_time | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |

---

#### 表: assets_wallet_information
**表注释**: 钱包信息表
**缺失注释字段数**: 8/17

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| wallet_info_id | bigint | PRI | auto_increment | 主键ID |
| app_id | bigint |  |  | 数值 |
| chain | varchar | MUL |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| wallet_address | varchar |  |  | 地址 |
| wallet_name | varchar |  |  | 名称 |
| update_user | varchar |  |  | 文本内容 |
| wallet_id | bigint |  |  | 数值 |

---

#### 表: assets_withdraw_apply
**表注释**: 客户/商户提现申请表
**缺失注释字段数**: 10/41

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| apply_time | timestamp |  | DEFAULT_GENERATED | 时间 |
| audit_time | timestamp |  |  | 时间 |
| txid | varchar |  |  | 文本内容 |
| withdraw_time | timestamp |  |  | 时间 |
| callback_time | timestamp |  |  | 时间 |
| fee_speed | varchar |  |  | 文本内容 |
| gas_price | decimal |  |  | 价格 |
| gas_limit | decimal |  |  | 数值 |
| nft_id | bigint |  |  | 数值 |
| batch_send_time | timestamp |  |  | 时间 |

---

#### 表: assets_withdraw_limit
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 18/22

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| limit_id | int | PRI | auto_increment | 主键ID |
| tenant_coin_id | bigint |  |  | 数值 |
| min_withdraw_amount | decimal |  |  | 金额 |
| max_withdraw_amount_once | decimal |  |  | 金额 |
| max_withdraw_amount_day | decimal |  |  | 金额 |
| day_start_time | time |  |  | 时间 |
| day_end_time | time |  |  | 时间 |
| day_once_audit_free | decimal |  |  | 数值 |
| day_one_audit_num_limit | decimal |  |  | 数量 |
| day_one_audit_amount_limit | decimal |  |  | 金额 |
| day_all_audit_amount_limit | decimal |  |  | 金额 |
| night_start_time | time |  |  | 时间 |
| night_end_time | time |  |  | 时间 |
| night_once_audit_free | decimal |  |  | 数值 |
| night_one_audit_num_limit | decimal |  |  | 数量 |
| night_one_audit_amount_limit | decimal |  |  | 金额 |
| night_all_audit_amount_limit | decimal |  |  | 金额 |
| update_user | varchar |  |  | 文本内容 |

---

#### 表: chain_coin_description
**表注释**: 币种的充提币说明
**缺失注释字段数**: 5/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| description_id | bigint | PRI | auto_increment | 主键ID |
| tenant_coin_id | bigint |  |  | 数值 |
| coin_id | bigint |  |  | 数值 |
| content | text |  |  | 文本内容 |
| update_user | varchar |  |  | 文本内容 |

---

#### 表: chain_coin_info
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 8/18

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| coin_id | bigint | PRI | auto_increment | 主键ID |
| chain | varchar | MUL |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| coin | varchar |  |  | 文本内容 |
| coin_icon | varchar |  |  | 文本内容 |
| contract_addr | varchar |  |  | 文本内容 |
| gas_limit | decimal |  |  | 数值 |
| update_user | varchar |  |  | 文本内容 |

---

#### 表: chain_contract_template
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 5/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| template_id | bigint | PRI | auto_increment | 主键ID |
| protocol | varchar |  |  | 文本内容 |
| contract_name | varchar |  |  | 名称 |
| contract_class_name | varchar |  |  | 名称 |
| update_user | varchar |  |  | 文本内容 |

---

#### 表: chain_info
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 10/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| chain_id | bigint | PRI | auto_increment | 主键ID |
| chain | varchar |  |  | 文本内容 |
| base_coin | varchar |  |  | 文本内容 |
| safe_confirms | int |  |  | 数值 |
| fast_confirms | int |  |  | 数值 |
| gas_tracker_api | varchar |  |  | 文本内容 |
| update_user | varchar |  |  | 文本内容 |
| low_gas_price | decimal |  |  | 价格 |
| average_gas_price | decimal |  |  | 价格 |
| high_gas_price | decimal |  |  | 价格 |

---

#### 表: chain_info_copy
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 10/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| chain_id | bigint | PRI | auto_increment | 主键ID |
| chain | varchar |  |  | 文本内容 |
| base_coin | varchar |  |  | 文本内容 |
| safe_confirms | int |  |  | 数值 |
| fast_confirms | int |  |  | 数值 |
| gas_tracker_api | varchar |  |  | 文本内容 |
| update_user | varchar |  |  | 文本内容 |
| low_gas_price | decimal |  |  | 价格 |
| average_gas_price | decimal |  |  | 价格 |
| high_gas_price | decimal |  |  | 价格 |

---

#### 表: chain_protocol_info
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 4/8

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| protocol_id | bigint | PRI | auto_increment | 主键ID |
| chain | varchar |  |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| update_user | varchar |  |  | 文本内容 |

---

#### 表: dms_wallet_address_modify_log
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 18/20

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| old_wallet_info_id | bigint |  |  | 数值 |
| new_wallet_info_id | bigint |  |  | 数值 |
| old_tenant_id | bigint |  |  | 数值 |
| new_tenant_id | bigint |  |  | 数值 |
| old_app_id | bigint |  |  | 数值 |
| new_app_id | bigint |  |  | 数值 |
| old_chain | varchar |  |  | 文本内容 |
| new_chain | varchar |  |  | 文本内容 |
| old_wallet_address | varchar |  |  | 地址 |
| new_wallet_address | varchar |  |  | 地址 |
| old_wallet_type | int |  |  | 类型 |
| new_wallet_type | int |  |  | 类型 |
| old_user_id | varchar |  |  | 文本内容 |
| new_user_id | varchar |  |  | 文本内容 |
| create_by | varchar |  |  | 创建人 |
| create_time | datetime |  |  | 创建时间 |
| update_by | varchar |  |  | 更新人 |
| update_time | datetime |  |  | 更新时间 |

---

#### 表: undo_log
**表注释**: seata分布式事务控制表
**缺失注释字段数**: 9/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| branch_id | bigint |  |  | 数值 |
| xid | varchar | MUL |  | 文本内容 |
| context | varchar |  |  | 文本内容 |
| rollback_info | longblob |  |  | 请补充注释 |
| log_status | int |  |  | 状态 |
| log_created | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| log_modified | timestamp |  | DEFAULT_GENERATED | 时间 |
| ext | varchar |  |  | 文本内容 |

---

#### 表: user_app_info
**表注释**: 商户App信息表，只保留基本信息，用于关联使用名称查询，其他配置在mini库
**缺失注释字段数**: 2/8

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| app_id | bigint | PRI | auto_increment | 主键ID |
| update_user | varchar |  |  | 文本内容 |

---

### 数据库: mini_config

#### 表: config_info
**表注释**: config_info
**缺失注释字段数**: 7/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| group_id | varchar |  |  | 文本内容 |
| app_name | varchar |  |  | 名称 |
| c_desc | varchar |  |  | 描述 |
| c_use | varchar |  |  | 文本内容 |
| effect | varchar |  |  | 文本内容 |
| type | varchar |  |  | 类型 |
| c_schema | text |  |  | 文本内容 |

---

#### 表: config_info_aggr
**表注释**: 增加租户字段
**缺失注释字段数**: 1/8

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| app_name | varchar |  |  | 名称 |

---

#### 表: config_tags_relation
**表注释**: config_tag_relation
**缺失注释字段数**: 1/7

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| nid | bigint | PRI | auto_increment | 主键ID |

---

#### 表: his_config_info
**表注释**: 多租户改造
**缺失注释字段数**: 11/13

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint |  |  | 数值 |
| nid | bigint | PRI | auto_increment | 主键ID |
| data_id | varchar | MUL |  | 文本内容 |
| group_id | varchar |  |  | 文本内容 |
| content | longtext |  |  | 文本内容 |
| md5 | varchar |  |  | 文本内容 |
| gmt_create | datetime | MUL |  | 时间 |
| gmt_modified | datetime | MUL |  | 时间 |
| src_user | text |  |  | 文本内容 |
| src_ip | varchar |  |  | 文本内容 |
| op_type | char |  |  | 类型 |

---

#### 表: permissions
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 3/3

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| role | varchar | PRI |  | 文本内容 |
| resource | varchar | PRI |  | 文本内容 |
| action | varchar | PRI |  | 文本内容 |

---

#### 表: roles
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 2/2

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| username | varchar | PRI |  | 名称 |
| role | varchar | PRI |  | 文本内容 |

---

#### 表: users
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 3/3

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| username | varchar | PRI |  | 名称 |
| password | varchar |  |  | 密码 |
| enabled | tinyint |  |  | 数值 |

---

### 数据库: mini_dms

#### 表: dms_wallet_address_modify_log
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 18/20

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| old_wallet_info_id | bigint |  |  | 数值 |
| new_wallet_info_id | bigint |  |  | 数值 |
| old_tenant_id | bigint |  |  | 数值 |
| new_tenant_id | bigint |  |  | 数值 |
| old_app_id | bigint |  |  | 数值 |
| new_app_id | bigint |  |  | 数值 |
| old_chain | varchar |  |  | 文本内容 |
| new_chain | varchar |  |  | 文本内容 |
| old_wallet_address | varchar |  |  | 地址 |
| new_wallet_address | varchar |  |  | 地址 |
| old_wallet_type | int |  |  | 类型 |
| new_wallet_type | int |  |  | 类型 |
| old_user_id | varchar |  |  | 文本内容 |
| new_user_id | varchar |  |  | 文本内容 |
| create_by | varchar |  |  | 创建人 |
| create_time | datetime |  |  | 创建时间 |
| update_by | varchar |  |  | 更新人 |
| update_time | datetime |  |  | 更新时间 |

---

### 数据库: mini_guess

#### 表: undo_log
**表注释**: seata分布式事务控制表
**缺失注释字段数**: 9/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| branch_id | bigint |  |  | 数值 |
| xid | varchar | MUL |  | 文本内容 |
| context | varchar |  |  | 文本内容 |
| rollback_info | longblob |  |  | 请补充注释 |
| log_status | int |  |  | 状态 |
| log_created | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| log_modified | timestamp |  | DEFAULT_GENERATED | 时间 |
| ext | varchar |  |  | 文本内容 |

---

### 数据库: mini_job

#### 表: qrtz_blob_triggers
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 4/4

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| trigger_name | varchar | PRI |  | 名称 |
| trigger_group | varchar | PRI |  | 文本内容 |
| blob_data | blob |  |  | 请补充注释 |

---

#### 表: qrtz_calendars
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 3/3

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| calendar_name | varchar | PRI |  | 名称 |
| calendar | blob |  |  | 请补充注释 |

---

#### 表: qrtz_cron_triggers
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 5/5

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| trigger_name | varchar | PRI |  | 名称 |
| trigger_group | varchar | PRI |  | 文本内容 |
| cron_expression | varchar |  |  | 文本内容 |
| time_zone_id | varchar |  |  | 文本内容 |

---

#### 表: qrtz_fired_triggers
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 13/13

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| entry_id | varchar | PRI |  | 主键ID |
| trigger_name | varchar |  |  | 名称 |
| trigger_group | varchar |  |  | 文本内容 |
| instance_name | varchar |  |  | 名称 |
| fired_time | bigint |  |  | 数值 |
| sched_time | bigint |  |  | 数值 |
| priority | int |  |  | 数值 |
| state | varchar |  |  | 文本内容 |
| job_name | varchar |  |  | 名称 |
| job_group | varchar |  |  | 文本内容 |
| is_nonconcurrent | varchar |  |  | 文本内容 |
| requests_recovery | varchar |  |  | 文本内容 |

---

#### 表: qrtz_job_details
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 10/10

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| job_name | varchar | PRI |  | 名称 |
| job_group | varchar | PRI |  | 文本内容 |
| description | varchar |  |  | 描述 |
| job_class_name | varchar |  |  | 名称 |
| is_durable | varchar |  |  | 文本内容 |
| is_nonconcurrent | varchar |  |  | 文本内容 |
| is_update_data | varchar |  |  | 文本内容 |
| requests_recovery | varchar |  |  | 文本内容 |
| job_data | blob |  |  | 请补充注释 |

---

#### 表: qrtz_locks
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 2/2

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| lock_name | varchar | PRI |  | 名称 |

---

#### 表: qrtz_paused_trigger_grps
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 2/2

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| trigger_group | varchar | PRI |  | 文本内容 |

---

#### 表: qrtz_scheduler_state
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 4/4

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| instance_name | varchar | PRI |  | 名称 |
| last_checkin_time | bigint |  |  | 数值 |
| checkin_interval | bigint |  |  | 数值 |

---

#### 表: qrtz_simple_triggers
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 6/6

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| trigger_name | varchar | PRI |  | 名称 |
| trigger_group | varchar | PRI |  | 文本内容 |
| repeat_count | bigint |  |  | 数量 |
| repeat_interval | bigint |  |  | 数值 |
| times_triggered | bigint |  |  | 数值 |

---

#### 表: qrtz_simprop_triggers
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 14/14

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| trigger_name | varchar | PRI |  | 名称 |
| trigger_group | varchar | PRI |  | 文本内容 |
| str_prop_1 | varchar |  |  | 文本内容 |
| str_prop_2 | varchar |  |  | 文本内容 |
| str_prop_3 | varchar |  |  | 文本内容 |
| int_prop_1 | int |  |  | 数值 |
| int_prop_2 | int |  |  | 数值 |
| long_prop_1 | bigint |  |  | 数值 |
| long_prop_2 | bigint |  |  | 数值 |
| dec_prop_1 | decimal |  |  | 数值 |
| dec_prop_2 | decimal |  |  | 数值 |
| bool_prop_1 | varchar |  |  | 文本内容 |
| bool_prop_2 | varchar |  |  | 文本内容 |

---

#### 表: qrtz_triggers
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 16/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| sched_name | varchar | PRI |  | 名称 |
| trigger_name | varchar | PRI |  | 名称 |
| trigger_group | varchar | PRI |  | 文本内容 |
| job_name | varchar |  |  | 名称 |
| job_group | varchar |  |  | 文本内容 |
| description | varchar |  |  | 描述 |
| next_fire_time | bigint |  |  | 数值 |
| prev_fire_time | bigint |  |  | 数值 |
| priority | int |  |  | 数值 |
| trigger_state | varchar |  |  | 文本内容 |
| trigger_type | varchar |  |  | 类型 |
| start_time | bigint |  |  | 数值 |
| end_time | bigint |  |  | 数值 |
| calendar_name | varchar |  |  | 名称 |
| misfire_instr | smallint |  |  | 数值 |
| job_data | blob |  |  | 请补充注释 |

---

#### 表: xxl_job_group
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 2/6

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| update_time | datetime |  |  | 更新时间 |

---

#### 表: xxl_job_info
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 4/24

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| job_desc | varchar |  |  | 描述 |
| add_time | datetime |  |  | 时间 |
| update_time | datetime |  |  | 更新时间 |

---

#### 表: xxl_job_log
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 1/15

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |

---

#### 表: xxl_job_log_report
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 2/6

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| update_time | datetime |  |  | 更新时间 |

---

#### 表: xxl_job_logglue
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 3/7

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| add_time | datetime |  |  | 时间 |
| update_time | datetime |  |  | 更新时间 |

---

#### 表: xxl_job_registry
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 5/5

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| registry_group | varchar | MUL |  | 文本内容 |
| registry_key | varchar |  |  | 键 |
| registry_value | varchar |  |  | 值 |
| update_time | datetime |  |  | 更新时间 |

---

#### 表: xxl_job_user
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 1/5

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |

---

### 数据库: mini_market

#### 表: tb_coin_app_market
**表注释**: 设置不同应用、服务返回的币对
**缺失注释字段数**: 1/5

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |

---

#### 表: tb_coin_market_off_market
**表注释**: 开休市管理
**缺失注释字段数**: 3/8

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| update_time | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 更新时间 |
| create_time | timestamp |  | DEFAULT_GENERATED | 创建时间 |

---

#### 表: tb_coin_market_theme
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 4/7

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| sort | int |  |  | 排序 |
| update_time | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 更新时间 |
| create_time | timestamp |  | DEFAULT_GENERATED | 创建时间 |

---

#### 表: tb_coin_turnover
**表注释**: 成交量浮动表
**缺失注释字段数**: 3/8

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| create_time | datetime |  | DEFAULT_GENERATED | 创建时间 |
| update_time | datetime |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 更新时间 |

---

#### 表: tb_off_market_template
**表注释**: 开休市模板名称
**缺失注释字段数**: 3/4

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |
| update_time | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 更新时间 |
| create_time | timestamp |  | DEFAULT_GENERATED | 创建时间 |

---

#### 表: tb_off_market_template_detail
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 6/6

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int |  |  | 数值 |
| template_id | int |  |  | 数值 |
| open_time | timestamp |  |  | 时间 |
| close_time | timestamp |  |  | 时间 |
| update_time | timestamp |  |  | 更新时间 |
| create_time | timestamp |  |  | 创建时间 |

---

#### 表: tb_push_topic_info
**表注释**: websocket topic 信息表
**缺失注释字段数**: 1/7

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | int | PRI | auto_increment | 主键ID |

---

#### 表: undo_log
**表注释**: seata分布式事务控制表
**缺失注释字段数**: 9/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| branch_id | bigint |  |  | 数值 |
| xid | varchar | MUL |  | 文本内容 |
| context | varchar |  |  | 文本内容 |
| rollback_info | longblob |  |  | 请补充注释 |
| log_status | int |  |  | 状态 |
| log_created | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| log_modified | timestamp |  | DEFAULT_GENERATED | 时间 |
| ext | varchar |  |  | 文本内容 |

---

### 数据库: mini_user

#### 表: undo_log
**表注释**: seata分布式事务控制表
**缺失注释字段数**: 9/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| branch_id | bigint |  |  | 数值 |
| xid | varchar | MUL |  | 文本内容 |
| context | varchar |  |  | 文本内容 |
| rollback_info | longblob |  |  | 请补充注释 |
| log_status | int |  |  | 状态 |
| log_created | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| log_modified | timestamp |  | DEFAULT_GENERATED | 时间 |
| ext | varchar |  |  | 文本内容 |

---

#### 表: user_activity_switch_cfg
**表注释**: 营销活动类型总开关配置
**缺失注释字段数**: 1/8

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |

---

#### 表: user_login_info
**表注释**: 用户登录信息表
**缺失注释字段数**: 2/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| last_login_ip | varchar |  |  | 文本内容 |
| last_login_terminal | varchar |  |  | 文本内容 |

---

#### 表: user_operation_log
**表注释**: 用户操作日志表
**缺失注释字段数**: 1/15

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| device_id | varchar |  |  | 文本内容 |

---

#### 表: user_quick_link_cfg
**表注释**: 金刚区配置
**缺失注释字段数**: 1/15

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| unsupported_versions | varchar |  |  | 版本 |

---

#### 表: user_sms_record
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 1/16

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI |  | 主键ID |

---

### 数据库: mini_wallet

#### 表: undo_log
**表注释**: seata分布式事务控制表
**缺失注释字段数**: 9/9

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| branch_id | bigint |  |  | 数值 |
| xid | varchar | MUL |  | 文本内容 |
| context | varchar |  |  | 文本内容 |
| rollback_info | longblob |  |  | 请补充注释 |
| log_status | int |  |  | 状态 |
| log_created | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 时间 |
| log_modified | timestamp |  | DEFAULT_GENERATED | 时间 |
| ext | varchar |  |  | 文本内容 |

---

#### 表: wallet_block_scanner
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 6/7

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| id | bigint | PRI | auto_increment | 主键ID |
| chain | varchar |  |  | 文本内容 |
| block_hash | varchar |  |  | 文本内容 |
| block_height | bigint |  |  | 数值 |
| create_time | timestamp |  | DEFAULT_GENERATED | 创建时间 |
| update_time | timestamp |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP | 更新时间 |

---

#### 表: wallet_in_record
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 10/19

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| chain | varchar |  |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| from_address | varchar |  |  | 地址 |
| to_address | varchar |  |  | 地址 |
| amount | decimal |  |  | 金额 |
| txid | varchar |  |  | 文本内容 |
| confirm_num | int |  |  | 数量 |
| confirm_time | timestamp |  |  | 时间 |
| tx_fee | decimal |  |  | 数值 |
| log_index | varchar |  |  | 文本内容 |

---

#### 表: wallet_info
**表注释**: 钱包地址信息
**缺失注释字段数**: 7/18

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| wallet_id | bigint | PRI | auto_increment | 主键ID |
| tenant_id | bigint |  |  | 数值 |
| app_id | bigint |  |  | 数值 |
| chain | varchar |  |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| coin | varchar |  |  | 文本内容 |
| wallet_address | varchar |  |  | 地址 |

---

#### 表: wallet_nft_deploy_info
**表注释**: NFT合约deploy信息
**缺失注释字段数**: 6/22

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| confirm_num | int |  |  | 数量 |
| confirm_time | timestamp |  |  | 时间 |
| tx_fee | decimal |  |  | 数值 |
| txid | varchar |  |  | 文本内容 |
| gas_price | decimal |  |  | 价格 |
| gas_limit | decimal |  |  | 数值 |

---

#### 表: wallet_nft_mint_info
**表注释**: NFTmint表
**缺失注释字段数**: 7/27

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| mint_id | bigint | PRI | auto_increment | 主键ID |
| confirm_num | int |  |  | 数量 |
| confirm_time | timestamp |  |  | 时间 |
| tx_fee | decimal |  |  | 数值 |
| txid | varchar |  |  | 文本内容 |
| gas_price | decimal |  |  | 价格 |
| gas_limit | decimal |  |  | 数值 |

---

#### 表: wallet_out_record
**表注释**: ⚠️ 缺少表注释
**缺失注释字段数**: 13/22

**缺失注释的字段**:

| 字段名 | 数据类型 | 键类型 | 额外信息 | 建议注释 |
|--------|----------|--------|----------|----------|
| record_id | bigint | PRI | auto_increment | 主键ID |
| chain | varchar |  |  | 文本内容 |
| protocol | varchar |  |  | 文本内容 |
| from_address | varchar |  |  | 地址 |
| to_address | varchar |  |  | 地址 |
| amount | decimal |  |  | 金额 |
| txid | varchar |  |  | 文本内容 |
| confirm_num | int |  |  | 数量 |
| confirm_time | timestamp |  |  | 时间 |
| tx_fee | decimal |  |  | 数值 |
| gas_price | decimal |  |  | 价格 |
| gas_limit | decimal |  |  | 数值 |
| log_index | varchar |  |  | 文本内容 |

---

