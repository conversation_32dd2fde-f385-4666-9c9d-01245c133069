# 数据库: mini_dms

**分析时间**: 2025/7/24 18:11:25

**表数量**: 6

**表列表**: dms_monitor_alarm_cfg, dms_monitor_alarm_detail_cfg, dms_monitor_alarm_record, dms_notify_cfg, dms_telegram_bot_cfg, dms_wallet_address_modify_log

---

## 表名: dms_monitor_alarm_cfg

**表注释**: 监控预警配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| tenant_id | bigint(19) | 否 |  | 商户id |  |  |
| monitor_code | varchar(32) | 否 |  | 监控code |  |  |
| monitor_name | varchar(128) | 否 |  | 监控名称 |  |  |
| enable_switch | tinyint(3) | 否 |  | 生效开关;（0-不生效；1-生效） |  |  |
| content_template | varchar(512) | 否 |  | 预警内容模板 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | datetime | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | datetime | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: dms_monitor_alarm_detail_cfg

**表注释**: 监控预警详情配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| tenant_id | bigint(19) | 否 | 1 | 商户id |  |  |
| monitor_code | varchar(32) | 否 |  | 监控code |  |  |
| alarm_type | varchar(128) | 是 |  | 告警类型 |  |  |
| chain | varchar(32) | 是 |  | 链 |  |  |
| protocol | varchar(32) | 是 |  | 协议 |  |  |
| coin | varchar(32) | 是 |  | 币 |  |  |
| risk_level | tinyint(3) | 是 |  | 风险等级;（0-提醒；1-低风险；2-中风险；3-高风险） |  |  |
| monitor_value | varchar(256) | 是 |  | 监控值 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | datetime | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | datetime | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: dms_monitor_alarm_record

**表注释**: 监控告警记录表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增主键 | PRI |  |
| tenant_id | bigint(19) | 否 |  | 商户id |  |  |
| monitor_code | varchar(32) | 否 |  | 监控code | MUL |  |
| monitor_name | varchar(128) | 是 |  | 监控名称 |  |  |
| alarm_type | varchar(128) | 是 |  | 告警类型 |  |  |
| user_id | varchar(32) | 是 |  | uid | MUL |  |
| business_id | varchar(32) | 是 |  | 业务id |  |  |
| chain | varchar(32) | 是 |  | 链 |  |  |
| protocol | varchar(32) | 是 |  | 协议 |  |  |
| coin | varchar(32) | 是 |  | 币 |  |  |
| risk_level | tinyint(3) | 是 |  | 风险等级;（0-提醒；1-低风险；2-中风险；3-高风险） | MUL |  |
| monitor_value | varchar(256) | 是 |  | 监控值 |  |  |
| content | varchar(512) | 是 |  | 内容 |  |  |
| status | tinyint(3) | 是 |  | 状态;（0-未处理；1-已处理） | MUL |  |
| deal_remark | varchar(512) | 是 |  | 处理备注 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | datetime | 是 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | datetime | 是 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_monitor_code | monitor_code | 否 | BTREE |
| idx_risk_level | risk_level | 否 | BTREE |
| idx_status | status | 否 | BTREE |
| idx_user_id | user_id | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: dms_notify_cfg

**表注释**: 通知配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| notify_method | varchar(32) | 否 |  | 通知方式(TELEGRAM-电报；EMAIL：电子邮件) |  |  |
| bot_username | varchar(128) | 是 |  | 机器人用户名 |  |  |
| notify_account | varchar(128) | 否 |  | 通知账号 |  |  |
| risk_level | tinyint(3) | 是 |  | 预警等级 |  |  |
| tenant_id | bigint(19) | 是 |  | 商户ID |  |  |
| app_id | bigint(19) | 是 |  | AppID |  |  |
| user_id | bigint(19) | 是 |  | 用户ID |  |  |
| enable_switch | tinyint(3) | 否 |  | 启用开关（0-关闭，1-开启） |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | datetime | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | datetime | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: dms_telegram_bot_cfg

**表注释**: telegram机器人配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| bot_nickname | varchar(128) | 否 |  | 机器人昵称 |  |  |
| bot_username | varchar(128) | 否 |  | 机器人用户名 |  |  |
| bot_token | varchar(128) | 否 |  | 机器人token |  |  |
| status | tinyint(3) | 否 |  | 状态（0-未启用；1-启用） |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | datetime | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | datetime | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: dms_wallet_address_modify_log

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 主键 | PRI | auto_increment |
| old_wallet_info_id | bigint(19) | 否 |  | 无注释 |  |  |
| new_wallet_info_id | bigint(19) | 是 |  | 无注释 |  |  |
| old_tenant_id | bigint(19) | 否 |  | 无注释 |  |  |
| new_tenant_id | bigint(19) | 是 |  | 无注释 |  |  |
| old_app_id | bigint(19) | 否 |  | 无注释 |  |  |
| new_app_id | bigint(19) | 是 |  | 无注释 |  |  |
| old_chain | varchar(32) | 否 |  | 无注释 |  |  |
| new_chain | varchar(32) | 是 |  | 无注释 |  |  |
| old_wallet_address | varchar(256) | 否 |  | 无注释 |  |  |
| new_wallet_address | varchar(256) | 是 |  | 无注释 |  |  |
| old_wallet_type | int(10) | 否 |  | 无注释 |  |  |
| new_wallet_type | int(10) | 是 |  | 无注释 |  |  |
| old_user_id | varchar(32) | 否 |  | 无注释 |  |  |
| new_user_id | varchar(32) | 是 |  | 无注释 |  |  |
| create_by | varchar(128) | 否 |  | 无注释 |  |  |
| create_time | datetime | 否 |  | 无注释 |  |  |
| update_by | varchar(128) | 否 |  | 无注释 |  |  |
| update_time | datetime | 否 |  | 无注释 |  |  |
| status | tinyint(3) | 否 | 0 | 0-未处理，1-已处理 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

