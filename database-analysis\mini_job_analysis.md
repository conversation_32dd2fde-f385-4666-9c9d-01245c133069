# 数据库: mini_job

**分析时间**: 2025/7/24 18:11:25

**表数量**: 21

**表列表**: qrtz_blob_triggers, qrtz_calendars, qrtz_cron_triggers, qrtz_fired_triggers, qrtz_job_details, qrtz_locks, qrtz_paused_trigger_grps, qrtz_scheduler_state, qrtz_simple_triggers, qrtz_simprop_triggers, qrtz_triggers, sys_job, sys_job_log, xxl_job_group, xxl_job_info, xxl_job_lock, xxl_job_log, xxl_job_log_report, xxl_job_logglue, xxl_job_registry, xxl_job_user

---

## 表名: qrtz_blob_triggers

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| trigger_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| trigger_group | varchar(200) | 否 |  | 无注释 | PRI |  |
| blob_data | blob(65535) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | trigger_name | 是 | BTREE |
| PRIMARY | trigger_group | 是 | BTREE |

---

## 表名: qrtz_calendars

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| calendar_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| calendar | blob(65535) | 否 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | calendar_name | 是 | BTREE |

---

## 表名: qrtz_cron_triggers

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| trigger_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| trigger_group | varchar(200) | 否 |  | 无注释 | PRI |  |
| cron_expression | varchar(200) | 否 |  | 无注释 |  |  |
| time_zone_id | varchar(80) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | trigger_name | 是 | BTREE |
| PRIMARY | trigger_group | 是 | BTREE |

---

## 表名: qrtz_fired_triggers

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| entry_id | varchar(95) | 否 |  | 无注释 | PRI |  |
| trigger_name | varchar(200) | 否 |  | 无注释 |  |  |
| trigger_group | varchar(200) | 否 |  | 无注释 |  |  |
| instance_name | varchar(200) | 否 |  | 无注释 |  |  |
| fired_time | bigint(19) | 否 |  | 无注释 |  |  |
| sched_time | bigint(19) | 否 |  | 无注释 |  |  |
| priority | int(10) | 否 |  | 无注释 |  |  |
| state | varchar(16) | 否 |  | 无注释 |  |  |
| job_name | varchar(200) | 是 |  | 无注释 |  |  |
| job_group | varchar(200) | 是 |  | 无注释 |  |  |
| is_nonconcurrent | varchar(1) | 是 |  | 无注释 |  |  |
| requests_recovery | varchar(1) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | entry_id | 是 | BTREE |

---

## 表名: qrtz_job_details

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| job_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| job_group | varchar(200) | 否 |  | 无注释 | PRI |  |
| description | varchar(250) | 是 |  | 无注释 |  |  |
| job_class_name | varchar(250) | 否 |  | 无注释 |  |  |
| is_durable | varchar(1) | 否 |  | 无注释 |  |  |
| is_nonconcurrent | varchar(1) | 否 |  | 无注释 |  |  |
| is_update_data | varchar(1) | 否 |  | 无注释 |  |  |
| requests_recovery | varchar(1) | 否 |  | 无注释 |  |  |
| job_data | blob(65535) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | job_name | 是 | BTREE |
| PRIMARY | job_group | 是 | BTREE |

---

## 表名: qrtz_locks

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| lock_name | varchar(40) | 否 |  | 无注释 | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | lock_name | 是 | BTREE |

---

## 表名: qrtz_paused_trigger_grps

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| trigger_group | varchar(200) | 否 |  | 无注释 | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | trigger_group | 是 | BTREE |

---

## 表名: qrtz_scheduler_state

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| instance_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| last_checkin_time | bigint(19) | 否 |  | 无注释 |  |  |
| checkin_interval | bigint(19) | 否 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | instance_name | 是 | BTREE |

---

## 表名: qrtz_simple_triggers

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| trigger_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| trigger_group | varchar(200) | 否 |  | 无注释 | PRI |  |
| repeat_count | bigint(19) | 否 |  | 无注释 |  |  |
| repeat_interval | bigint(19) | 否 |  | 无注释 |  |  |
| times_triggered | bigint(19) | 否 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | trigger_name | 是 | BTREE |
| PRIMARY | trigger_group | 是 | BTREE |

---

## 表名: qrtz_simprop_triggers

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| trigger_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| trigger_group | varchar(200) | 否 |  | 无注释 | PRI |  |
| str_prop_1 | varchar(512) | 是 |  | 无注释 |  |  |
| str_prop_2 | varchar(512) | 是 |  | 无注释 |  |  |
| str_prop_3 | varchar(512) | 是 |  | 无注释 |  |  |
| int_prop_1 | int(10) | 是 |  | 无注释 |  |  |
| int_prop_2 | int(10) | 是 |  | 无注释 |  |  |
| long_prop_1 | bigint(19) | 是 |  | 无注释 |  |  |
| long_prop_2 | bigint(19) | 是 |  | 无注释 |  |  |
| dec_prop_1 | decimal(13,4) | 是 |  | 无注释 |  |  |
| dec_prop_2 | decimal(13,4) | 是 |  | 无注释 |  |  |
| bool_prop_1 | varchar(1) | 是 |  | 无注释 |  |  |
| bool_prop_2 | varchar(1) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | trigger_name | 是 | BTREE |
| PRIMARY | trigger_group | 是 | BTREE |

---

## 表名: qrtz_triggers

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| sched_name | varchar(120) | 否 |  | 无注释 | PRI |  |
| trigger_name | varchar(200) | 否 |  | 无注释 | PRI |  |
| trigger_group | varchar(200) | 否 |  | 无注释 | PRI |  |
| job_name | varchar(200) | 否 |  | 无注释 |  |  |
| job_group | varchar(200) | 否 |  | 无注释 |  |  |
| description | varchar(250) | 是 |  | 无注释 |  |  |
| next_fire_time | bigint(19) | 是 |  | 无注释 |  |  |
| prev_fire_time | bigint(19) | 是 |  | 无注释 |  |  |
| priority | int(10) | 是 |  | 无注释 |  |  |
| trigger_state | varchar(16) | 否 |  | 无注释 |  |  |
| trigger_type | varchar(8) | 否 |  | 无注释 |  |  |
| start_time | bigint(19) | 否 |  | 无注释 |  |  |
| end_time | bigint(19) | 是 |  | 无注释 |  |  |
| calendar_name | varchar(200) | 是 |  | 无注释 |  |  |
| misfire_instr | smallint(5) | 是 |  | 无注释 |  |  |
| job_data | blob(65535) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | sched_name | 是 | BTREE |
| PRIMARY | trigger_name | 是 | BTREE |
| PRIMARY | trigger_group | 是 | BTREE |
| sched_name | sched_name | 否 | BTREE |
| sched_name | job_name | 否 | BTREE |
| sched_name | job_group | 否 | BTREE |

---

## 表名: sys_job

**表注释**: 定时任务调度表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| job_id | bigint(19) | 否 |  | 任务id | PRI |  |
| job_name | varchar(64) | 否 |  | 任务名称 | PRI |  |
| job_group | varchar(64) | 否 |  | 任务组名 | PRI |  |
| job_order | char(1) | 是 | 1 | 组内执行顺利，值越大执行优先级越高，最大值9，最小值1 |  |  |
| job_type | char(1) | 否 | 1 | 1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他 |  |  |
| execute_path | varchar(500) | 是 |  | job_type=3时，rest调用地址，仅支持rest get协议,需要增加String返回值，0成功，1失败;job_type=4时，jar路径;其它值为空 |  |  |
| class_name | varchar(500) | 是 |  | job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空 |  |  |
| method_name | varchar(500) | 是 |  | 任务方法 |  |  |
| method_params_value | varchar(2000) | 是 |  | 参数值 |  |  |
| cron_expression | varchar(255) | 是 |  | cron执行表达式 |  |  |
| misfire_policy | varchar(20) | 是 | 3 | 错失执行策略（1错失周期立即执行 2错失周期执行一次 3下周期执行） |  |  |
| job_tenant_type | char(1) | 是 | 1 | 1、多租户任务;2、非多租户任务 |  |  |
| job_status | char(1) | 是 | 0 | 状态（1、未发布;2、运行中;3、暂停;4、删除;） |  |  |
| job_execute_status | char(1) | 是 | 0 | 状态（0正常 1异常） |  |  |
| create_by | varchar(64) | 是 |  | 创建者 |  |  |
| create_time | datetime | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_by | varchar(64) | 是 |  | 更新者 |  |  |
| update_time | datetime | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED |
| start_time | timestamp | 是 |  | 初次执行时间 |  |  |
| previous_time | timestamp | 是 |  | 上次执行时间 |  |  |
| next_time | timestamp | 是 |  | 下次执行时间 |  |  |
| tenant_id | bigint(19) | 是 | 1 | 租户 |  |  |
| remark | varchar(500) | 是 |  | 备注信息 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | job_id | 是 | BTREE |
| PRIMARY | job_name | 是 | BTREE |
| PRIMARY | job_group | 是 | BTREE |

---

## 表名: sys_job_log

**表注释**: 定时任务执行日志表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| job_log_id | bigint(19) | 否 |  | 任务日志ID | PRI |  |
| job_id | bigint(19) | 否 |  | 任务id |  |  |
| job_name | varchar(64) | 是 |  | 任务名称 |  |  |
| job_group | varchar(64) | 是 |  | 任务组名 |  |  |
| job_order | char(1) | 是 |  | 组内执行顺利，值越大执行优先级越高，最大值9，最小值1 |  |  |
| job_type | char(1) | 否 | 1 | 1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他 |  |  |
| execute_path | varchar(500) | 是 |  | job_type=3时，rest调用地址，仅支持post协议;job_type=4时，jar路径;其它值为空 |  |  |
| class_name | varchar(500) | 是 |  | job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空 |  |  |
| method_name | varchar(500) | 是 |  | 任务方法 |  |  |
| method_params_value | varchar(2000) | 是 |  | 参数值 |  |  |
| cron_expression | varchar(255) | 是 |  | cron执行表达式 |  |  |
| job_message | varchar(500) | 是 |  | 日志信息 |  |  |
| job_log_status | char(1) | 是 | 0 | 执行状态（0正常 1失败） |  |  |
| execute_time | varchar(30) | 是 |  | 执行时间 |  |  |
| exception_info | varchar(2000) | 是 |  | 异常信息 |  |  |
| create_time | datetime | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| tenant_id | bigint(19) | 否 | 1 | 租户id |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | job_log_id | 是 | BTREE |

---

## 表名: xxl_job_group

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| app_name | varchar(64) | 否 |  | 执行器AppName |  |  |
| title | varchar(12) | 否 |  | 执行器名称 |  |  |
| address_type | tinyint(3) | 否 | 0 | 执行器地址类型：0=自动注册、1=手动录入 |  |  |
| address_list | text(65535) | 是 |  | 执行器地址列表，多地址逗号分隔 |  |  |
| update_time | datetime | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: xxl_job_info

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| job_group | int(10) | 否 |  | 执行器主键ID |  |  |
| job_desc | varchar(255) | 否 |  | 无注释 |  |  |
| add_time | datetime | 是 |  | 无注释 |  |  |
| update_time | datetime | 是 |  | 无注释 |  |  |
| author | varchar(64) | 是 |  | 作者 |  |  |
| alarm_email | varchar(255) | 是 |  | 报警邮件 |  |  |
| schedule_type | varchar(50) | 否 | NONE | 调度类型 |  |  |
| schedule_conf | varchar(128) | 是 |  | 调度配置，值含义取决于调度类型 |  |  |
| misfire_strategy | varchar(50) | 否 | DO_NOTHING | 调度过期策略 |  |  |
| executor_route_strategy | varchar(50) | 是 |  | 执行器路由策略 |  |  |
| executor_handler | varchar(255) | 是 |  | 执行器任务handler |  |  |
| executor_param | varchar(512) | 是 |  | 执行器任务参数 |  |  |
| executor_block_strategy | varchar(50) | 是 |  | 阻塞处理策略 |  |  |
| executor_timeout | int(10) | 否 | 0 | 任务执行超时时间，单位秒 |  |  |
| executor_fail_retry_count | int(10) | 否 | 0 | 失败重试次数 |  |  |
| glue_type | varchar(50) | 否 |  | GLUE类型 |  |  |
| glue_source | mediumtext(16777215) | 是 |  | GLUE源代码 |  |  |
| glue_remark | varchar(128) | 是 |  | GLUE备注 |  |  |
| glue_updatetime | datetime | 是 |  | GLUE更新时间 |  |  |
| child_jobid | varchar(255) | 是 |  | 子任务ID，多个逗号分隔 |  |  |
| trigger_status | tinyint(3) | 否 | 0 | 调度状态：0-停止，1-运行 |  |  |
| trigger_last_time | bigint(19) | 否 | 0 | 上次调度时间 |  |  |
| trigger_next_time | bigint(19) | 否 | 0 | 下次调度时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: xxl_job_lock

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| lock_name | varchar(50) | 否 |  | 锁名称 | PRI |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | lock_name | 是 | BTREE |

---

## 表名: xxl_job_log

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| job_group | int(10) | 否 |  | 执行器主键ID |  |  |
| job_id | int(10) | 否 |  | 任务，主键ID |  |  |
| executor_address | varchar(255) | 是 |  | 执行器地址，本次执行的地址 |  |  |
| executor_handler | varchar(255) | 是 |  | 执行器任务handler |  |  |
| executor_param | varchar(512) | 是 |  | 执行器任务参数 |  |  |
| executor_sharding_param | varchar(20) | 是 |  | 执行器任务分片参数，格式如 1/2 |  |  |
| executor_fail_retry_count | int(10) | 否 | 0 | 失败重试次数 |  |  |
| trigger_time | datetime | 是 |  | 调度-时间 | MUL |  |
| trigger_code | int(10) | 否 |  | 调度-结果 |  |  |
| trigger_msg | text(65535) | 是 |  | 调度-日志 |  |  |
| handle_time | datetime | 是 |  | 执行-时间 |  |  |
| handle_code | int(10) | 否 |  | 执行-状态 | MUL |  |
| handle_msg | text(65535) | 是 |  | 执行-日志 |  |  |
| alarm_status | tinyint(3) | 否 | 0 | 告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| I_handle_code | handle_code | 否 | BTREE |
| I_trigger_time | trigger_time | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: xxl_job_log_report

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| trigger_day | datetime | 是 |  | 调度-时间 | UNI |  |
| running_count | int(10) | 否 | 0 | 运行中-日志数量 |  |  |
| suc_count | int(10) | 否 | 0 | 执行成功-日志数量 |  |  |
| fail_count | int(10) | 否 | 0 | 执行失败-日志数量 |  |  |
| update_time | datetime | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| i_trigger_day | trigger_day | 是 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: xxl_job_logglue

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| job_id | int(10) | 否 |  | 任务，主键ID |  |  |
| glue_type | varchar(50) | 是 |  | GLUE类型 |  |  |
| glue_source | mediumtext(16777215) | 是 |  | GLUE源代码 |  |  |
| glue_remark | varchar(128) | 否 |  | GLUE备注 |  |  |
| add_time | datetime | 是 |  | 无注释 |  |  |
| update_time | datetime | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: xxl_job_registry

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| registry_group | varchar(50) | 否 |  | 无注释 | MUL |  |
| registry_key | varchar(255) | 否 |  | 无注释 |  |  |
| registry_value | varchar(255) | 否 |  | 无注释 |  |  |
| update_time | datetime | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| i_g_k_v | registry_group | 否 | BTREE |
| i_g_k_v | registry_key | 否 | BTREE |
| i_g_k_v | registry_value | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: xxl_job_user

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 无注释 | PRI | auto_increment |
| username | varchar(50) | 否 |  | 账号 | UNI |  |
| password | varchar(50) | 否 |  | 密码 |  |  |
| role | tinyint(3) | 否 |  | 角色：0-普通用户、1-管理员 |  |  |
| permission | varchar(255) | 是 |  | 权限：执行器ID列表，多个逗号分割 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| i_username | username | 是 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

