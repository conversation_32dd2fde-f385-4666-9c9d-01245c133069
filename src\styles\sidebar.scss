.avue-sidebar {
    user-select: none; 
    position: relative;
    padding-top: 74px;
    height: 100%;
    position: relative;
    background-color: #20222a;
    transition: width .2s;
    box-sizing: border-box;
    box-shadow: 2px 0 6px rgba(0,21,41,.35);
    &--tip{
        width:90%;
        height: 140px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        position: absolute;
        top:5px;
        left:5%;
        color:#ccc;
        z-index: 2;
        text-align: center;
        font-size: 14px;
        background-color: rgba(0,0,0,.4);
    }
    .el-menu-item,
    .el-submenu__title {
        font-size: 14px;
        height: 56px;
        line-height: 56px;
    }
    .el-menu-item {
        span,i{
            color:rgba(255, 255, 255, 0.7);
        }
        &:hover {
            background-color: transparent;
            color: #fff;
            span,
            i {
                color: #fff;
            }
        }
        &.is-active {
            background-color: rgba(0, 0, 0, .8);
            span,
            i {
                color: #fff;
            }
            &:hover {
                background-color: rgba(0, 0, 0, .8);
            }
            &::before {
                content: " ";
                top: 0;
                left: 0;
                bottom: 0;
                width: 4px;
                background: $mainBg;
                position: absolute
            }
        }
    }
    .el-submenu__title {
        span,i{
            color:rgba(255, 255, 255, 0.7);
        }
        &:hover {
            i,
            span {
                color: #fff;
            }
            background-color:transparent ;
        }
    }
    .el-submenu .el-menu-item {
        height: 50px;
        line-height: 50px;
        span,i{
            color:rgba(255, 255, 255, 0.7);
        }
        &.is-active {
            background-color: rgba(0, 0, 0, .8);
            span,
            i {
                color: #fff
            }
            &:hover {
                background-color: rgba(0, 0, 0, .8);
            }
        }
        &:hover {
            background-color: transparent;
            span,
            i {
                color: #fff;
            }
        }
    }
}