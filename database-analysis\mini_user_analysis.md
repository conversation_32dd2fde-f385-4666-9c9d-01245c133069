# 数据库: mini_user

**分析时间**: 2025/7/24 18:11:26

**表数量**: 28

**表列表**: undo_log, user_activity_switch_cfg, user_address, user_banner_cfg, user_broker, user_business_new_record, user_business_switch_cfg, user_customer_chat, user_favorite_coin_cfg, user_function_switch_cfg, user_grade, user_grade_cfg, user_grade_record, user_home_page_cfg, user_invite_pic, user_invite_rela, user_invite_reward_cfg, user_invite_reward_log, user_login_info, user_operation_log, user_personal_info, user_quick_link_cfg, user_real_name_authentication_record, user_real_name_business_cfg, user_real_name_business_region_cfg, user_sms_record, user_system_version_cfg, user_wallet_address

---

## 表名: undo_log

**表注释**: seata分布式事务控制表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| branch_id | bigint(19) | 否 |  | 无注释 |  |  |
| xid | varchar(100) | 否 |  | 无注释 | MUL |  |
| context | varchar(128) | 否 |  | 无注释 |  |  |
| rollback_info | longblob(4294967295) | 否 |  | 无注释 |  |  |
| log_status | int(10) | 否 |  | 无注释 |  |  |
| log_created | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| log_modified | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| ext | varchar(100) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| ux_undo_log | xid | 是 | BTREE |
| ux_undo_log | branch_id | 是 | BTREE |

---

## 表名: user_activity_switch_cfg

**表注释**: 营销活动类型总开关配置

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| activity_name | varchar(255) | 否 |  | 活动名称 |  |  |
| activity_type | varchar(32) | 否 |  | 活动类型 | UNI |  |
| status | tinyint(3) | 否 |  | 开关 0关闭 1启用 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新人 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| type_index | activity_type | 是 | BTREE |

---

## 表名: user_address

**表注释**: 用户地址表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| name | varchar(64) | 否 |  | 国家/地区 |  |  |
| phone | varchar(32) | 否 |  | 号码 |  |  |
| postal | varchar(32) | 是 |  | 邮政编码 |  |  |
| area_code | varchar(32) | 否 |  | 区域号码 |  |  |
| country | varchar(32) | 否 |  | 国家/地区 |  |  |
| province | varchar(32) | 否 |  | 地区表中省份的ID |  |  |
| city | varchar(32) | 否 |  | 地区表中城市的ID |  |  |
| region | varchar(32) | 否 |  | 地区表中的区ID |  |  |
| address | varchar(1024) | 否 |  | 具体的地址 |  |  |
| is_default | tinyint(3) | 否 |  | 是否默认 |  |  |
| addr_json | varchar(1024) | 是 |  | 附加json 包含省市区国际化数据  |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| index_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_banner_cfg

**表注释**: banner配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| group_id | bigint(19) | 是 |  | 组id |  |  |
| client_type | varchar(32) | 是 |  | 客户端类型：ClientTypeEnum
（
GENERAL：通用；
iOS：iOS；
Android：Android；
H5：H5
PC：电脑
） | MUL |  |
| banner_type | varchar(32) | 否 |  | banner类型：BannerTypeEnum
（
REVISED_HOME_PAGE：改版首页
HOME_PAGE_BIG_BANNER：首页大banner；HOME_PAGE_SMALL_BANNER：首页小banner；INFORMATION_BANNER：资讯banner；LIVE_BROADCAST_BANNER：直播banner；MEAL_TICKET_BANNER：饭票banner；CAR_LIFE_BANNER：车生活banner
） | MUL |  |
| banner_name | varchar(64) | 是 |  | banner名称 | MUL |  |
| banner_describe | varchar(512) | 是 |  | banner描述 |  |  |
| banner_image_link_text_key | varchar(64) | 否 |  | banner图片链接文本key |  |  |
| link_type | tinyint(3) | 是 | 2 | 链接类型 1-CG 2-第三方 |  |  |
| banner_jump_link | varchar(256) | 是 |  | banner跳转链接 |  |  |
| banner_sort | int(10) | 是 |  | banner排序 |  |  |
| coordinate | varchar(32) | 是 |  | 坐标 |  |  |
| enable_switch | varchar(32) | 是 |  | 上架开关：SwitchValueTypeEnum
（
ON：开启；
OFF：关闭
） |  |  |
| enable_time | bigint(19) | 是 |  | 生效时间 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_banner_name | banner_name | 否 | BTREE |
| idx_banner_type | banner_type | 否 | BTREE |
| idx_client_type | client_type | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_broker

**表注释**: 特殊经纪商

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| status | tinyint(3) | 否 |  | 状态 0不启用 1-启用 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| index_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_business_new_record

**表注释**: 业务新用户记录表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| uid | int(10) | 否 |  | uid |  |  |
| key | varchar(64) | 否 |  | 业务类型 |  |  |
| value | tinyint(3) | 否 |  | 业务值（1-新用户） |  |  |
| remark | varchar(255) | 是 |  | 备注 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_business_switch_cfg

**表注释**: 用户业务开关配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI | auto_increment |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| switch_type | varchar(32) | 否 |  | 开关类型：BusinessSwitchTypeEnum
（
EXCHANGE_COIN：兑换币；
WITHDRAWAL_COIN：提币；
RECHARGE_COIN：充币；
WITHDRAWAL_NFT：提现NFT；
RECHARGE_NFT：充值NFT
） | MUL |  |
| switch_value | varchar(32) | 否 |  | 开关值：SwitchValueTypeEnum
（
ON：开启；
OFF：关闭
） |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_switch_type | switch_type | 否 | BTREE |
| idx_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_customer_chat

**表注释**: 用户客服聊天id关联表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| chat_id | varchar(64) | 否 |  | 客服聊天id |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| index_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_favorite_coin_cfg

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| coin | varchar(128) | 否 |  | 币种 | MUL |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |
| asset_type | tinyint(3) | 否 |  | 资产类型(1-代币；2-NFT；3-法币) |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_coin | coin | 否 | BTREE |
| idx_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_function_switch_cfg

**表注释**: 功能开关配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增主键 | PRI |  |
| function_key | varchar(64) | 否 |  | 功能key |  |  |
| function_name | varchar(64) | 否 |  | 功能名 |  |  |
| function_type | int(10) | 否 |  | 功能类型 |  |  |
| function_describe | varchar(128) | 是 |  | 功能描述 |  |  |
| client_type | varchar(32) | 否 |  | 客户端类型 |  |  |
| unsupported_version | varchar(32) | 是 |  | 不支持的版本 |  |  |
| function_sort | int(10) | 是 |  | 功能排序 |  |  |
| function_switch | tinyint(3) | 否 |  | 功能开关（0-关；1-开） |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_grade

**表注释**: 经纪商等级表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| uid | int(10) | 否 |  | uid |  |  |
| grade_level | tinyint(3) | 否 |  | 经纪人级别 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_grade_cfg

**表注释**: 经纪商等级配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| grade_level | int(10) | 否 |  | 等级 |  |  |
| name | varchar(64) | 否 |  | 等级名称 |  |  |
| type | tinyint(3) | 否 |  | 条件类型：1-最近30天入金 |  |  |
| enable_level | tinyint(3) | 是 |  | 入金层级 等级为1时 0 表示自身 空表示不限制 |  |  |
| coin | varchar(32) | 否 |  | 币种 |  |  |
| qty | decimal(32,16) | 否 |  | 数量 |  |  |
| status | tinyint(3) | 否 |  | 状态 0-不可用 1-可用 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_grade_record

**表注释**: 经纪商等级记录表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI | auto_increment |
| uid | int(10) | 否 |  | uid |  |  |
| grade_level | tinyint(3) | 否 |  | 经纪人级别id |  |  |
| calc_level | tinyint(3) | 否 |  | 已计算子级 |  |  |
| calc_level_sum | decimal(36,18) | 否 |  | 已计算子级总额 |  |  |
| additional_json | varchar(255) | 是 |  | 附加数据（用户及入金数据） |  |  |
| next_level | tinyint(3) | 是 |  | 下个级别子级层数 |  |  |
| next_level_sum | decimal(36,18) | 是 |  | 下个级别入金总额 |  |  |
| create_by | varchar(32) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(32) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_home_page_cfg

**表注释**: 首页配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| group_name | varchar(256) | 否 |  | 组名称 |  |  |
| layout_type | tinyint(3) | 否 |  | 布局类型（0-单行多列；1-多行多列） |  |  |
| group_sort | int(10) | 否 |  | 组排序 |  |  |
| client_type | varchar(32) | 否 |  | 客户端类型 |  |  |
| enable_switch | tinyint(3) | 否 |  | 上架开关（0-未上架，1-已上架） |  |  |
| create_by | varchar(32) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(32) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_invite_pic

**表注释**: 邀请好友图片配置;

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| pic_name | varchar(64) | 是 |  | 图片名称 |  |  |
| pic_url | varchar(255) | 是 |  | 图片地址 |  |  |
| pic_language | varchar(32) | 是 |  | 图片语言 |  |  |
| pic_type | tinyint(3) | 是 |  | 图片类型;1邀请海报  2首页海报 3规则海报 |  |  |
| device_type | varchar(32) | 是 |  | 设备类型  GENERAL:通用 iOS:iOS  Android:安卓 H5：H5  PC：PC |  |  |
| status | tinyint(3) | 是 |  | 状态;0删除 1使用中 |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新人 |  |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_invite_rela

**表注释**: 邀请记录表;

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| invite_uid | int(10) | 是 |  | 邀请人id | MUL |  |
| invited_uid | int(10) | 是 |  | 被邀请者id | MUL |  |
| offset | int(10) | 是 |  | 层级;与每层邀请者关系，1表示直接邀请 |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新人 |  |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_invatedId | invited_uid | 否 | BTREE |
| idx_invateId | invite_uid | 否 | BTREE |
| idx_invateId | offset | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_invite_reward_cfg

**表注释**: 邀请奖励配置;

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| user_type | tinyint(3) | 是 |  | 奖励用户类型 1普通客户  2顶层客户 |  |  |
| invite_relax | tinyint(3) | 是 |  | 好友邀请关系;1直接好友 2间接好友 3直接+间接好友 |  |  |
| invite_num | int(10) | 是 |  | 邀请人数 |  |  |
| reward_type | tinyint(3) | 是 |  | 奖励类型 1积分 2法币 3 代币 |  |  |
| reward_name | varchar(32) | 是 |  | 奖励币种 |  |  |
| reward_num | decimal(36,18) | 是 |  | 奖励数量 |  |  |
| status | tinyint(3) | 是 |  | 状态 |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新人 |  |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_invite_reward_log

**表注释**: 邀请奖励记录;

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| uid | int(10) | 否 |  | 用户uid |  |  |
| invite_num | int(10) | 是 |  | 邀请人数 |  |  |
| reward_cfg_id | bigint(19) | 是 |  | 奖励配置id |  |  |
| reward_name | varchar(32) | 是 |  | 奖励名称 |  |  |
| reward_num | decimal(36,18) | 是 |  | 奖励数量 |  |  |
| status | tinyint(3) | 是 |  | 状态 |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新人 |  |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_login_info

**表注释**: 用户登录信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| google_verify_key | varchar(64) | 是 |  | 谷歌校验密钥 |  |  |
| google_verify_status | varchar(32) | 否 |  | 谷歌校验状态：GoogleVerifyStatusEnum
（
BIND：绑定；
UNBIND：解绑
） | MUL |  |
| salt | varchar(16) | 是 |  | 盐值 |  |  |
| login_password | varchar(128) | 是 |  | 登录密码 |  |  |
| funds_password | varchar(128) | 是 |  | 资金密码 |  |  |
| last_change_login_password_time | bigint(19) | 是 |  | 上次修改登录密码时间 |  |  |
| last_change_funds_password_time | bigint(19) | 是 |  | 上次修改资金密码时间 |  |  |
| login_switch | varchar(32) | 是 |  | 登录开关 | MUL |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |
| last_login_ip | varchar(64) | 是 |  | 无注释 |  |  |
| last_login_terminal | varchar(64) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_google_verify_status | google_verify_status | 否 | BTREE |
| idx_login_switch | login_switch | 否 | BTREE |
| idx_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_operation_log

**表注释**: 用户操作日志表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| operation_type | varchar(32) | 否 |  | 操作类型：UserOperationTypeEnum
（
REGISTER：注册；
LOGIN：登录
） | MUL |  |
| client_type | varchar(32) | 否 |  | 客户端类型：ClientTypeEnum
（
GENERAL：通用；
iOS：iOS；
Android：Android；
PC：电脑
） | MUL |  |
| version | int(10) | 否 |  | 版本号 | MUL |  |
| device | varchar(64) | 否 |  | 设备 | MUL |  |
| operating_system | varchar(32) | 是 |  | 操作系统 |  |  |
| browser | varchar(64) | 是 |  | 浏览器 |  |  |
| ip_address | varchar(64) | 否 |  | IP地址 | MUL |  |
| operation_time | bigint(19) | 否 |  | 操作时间 | MUL |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |
| device_id | varchar(64) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_client_type | client_type | 否 | BTREE |
| idx_device | device | 否 | BTREE |
| idx_ip_address | ip_address | 否 | BTREE |
| idx_operation_time | operation_time | 否 | BTREE |
| idx_operation_type | operation_type | 否 | BTREE |
| idx_uid | uid | 否 | BTREE |
| idx_version | version | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_personal_info

**表注释**: 用户个人信息表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| uid | int(10) | 否 |  | 用户uid | UNI |  |
| email | varchar(128) | 是 |  | 邮箱 | MUL |  |
| phone | varchar(16) | 是 |  | 手机号 | MUL |  |
| phone_area_code | varchar(16) | 是 |  | 手机区号 | MUL |  |
| nickname | varchar(128) | 是 |  | 昵称 | MUL |  |
| sex | varchar(16) | 是 |  | 性别;字典值 |  |  |
| avatar_link | varchar(256) | 是 |  | 头像链接 |  |  |
| language_preference | varchar(32) | 否 | zh_cn | 语言偏好;字典值 |  |  |
| preference_coin | varchar(255) | 否 |  | 偏好币种 |  |  |
| real_name_level | tinyint(3) | 否 | 0 | 实名认证等级（0-未认证；1-初级认证；2-高级认证） |  |  |
| user_type | varchar(32) | 否 |  | 用户类型：UserTypeEnum
（1：普通用户；
101：平台-手续费账户；
102：平台-兑换账户；
103：平台-资金调整账户；
104：平台-支出子账户） | MUL |  |
| user_status | varchar(32) | 否 |  | 用户状态：UserStatusEnum
（0：冻结；
1：正常；
2：销户） | MUL |  |
| invite_code | varchar(32) | 是 |  | 用户邀请码 | UNI |  |
| invite_uid | int(10) | 否 | -1 | 邀请人uid |  |  |
| invite_time | bigint(19) | 是 |  | 邀请时间 |  |  |
| wallet_addr | varchar(128) | 是 |  | 默认的钱包地址或telegramUid |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_email | email | 否 | BTREE |
| idx_nickname | nickname | 否 | BTREE |
| idx_phone | phone | 否 | BTREE |
| idx_phone_area_code | phone_area_code | 否 | BTREE |
| idx_user_status | user_status | 否 | BTREE |
| idx_user_type | user_type | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |
| unq_invite_code | invite_code | 是 | BTREE |
| unq_uid | uid | 是 | BTREE |

---

## 表名: user_quick_link_cfg

**表注释**: 金刚区配置

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| quick_link_key | varchar(32) | 否 |  | 金刚区key | MUL |  |
| quick_link_name_text_key | varchar(64) | 否 |  | 名称文本key |  |  |
| client_type | varchar(32) | 否 |  | 客户端类型：ClientTypeEnum
（
GENERAL：通用；
iOS：iOS；
Android：Android；
PC：电脑
） | MUL |  |
| quick_link_img_link_day | varchar(256) | 否 |  | 日间图片链接 |  |  |
| quick_link_img_link_night | varchar(256) | 否 |  | 夜间图片链接 |  |  |
| unsupported_versions | varchar(512) | 是 |  | 无注释 |  |  |
| quick_link_sort | int(10) | 否 |  | 排序 |  |  |
| quick_link | varchar(256) | 是 |  | 链接 |  |  |
| enable_switch | varchar(32) | 否 |  | 上架开关：SwitchValueTypeEnum
（
ON：开启；
OFF：关闭
） |  |  |
| need_login_flag | varchar(32) | 否 |  | 需要登录标识：FlagTypeEnum
（
Y：yes；
N：no
） |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_client_type | client_type | 否 | BTREE |
| idx_quick_link_key | quick_link_key | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_real_name_authentication_record

**表注释**: 用户实名认证记录表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增主键 | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| current_real_name_level | tinyint(3) | 否 |  | 当前认证级别（0-未认证；1-初级认证；2-高级认证） | MUL |  |
| authentication_type | tinyint(3) | 否 |  | 认证类型（1-初级认证；2-高级认证） | MUL |  |
| real_name | varchar(256) | 是 |  | 真实姓名 | MUL |  |
| country_code | varchar(32) | 是 |  | 国家编码 | MUL |  |
| certificate_type | tinyint(3) | 是 |  | 证件类型（1-身份证；2-护照；3-驾照） |  |  |
| certificate_number | varchar(64) | 是 |  | 证件号码 | MUL |  |
| certificate_front_photo | varchar(256) | 是 |  | 证件正面照 |  |  |
| certificate_back_photo | varchar(256) | 是 |  | 证件反面照 |  |  |
| province | varchar(128) | 是 |  | 省 |  |  |
| city | varchar(128) | 是 |  | 市 |  |  |
| detail_address | varchar(512) | 是 |  | 详细地址 |  |  |
| video_captcha | varchar(16) | 是 |  | 视频验证码 |  |  |
| verify_video | varchar(256) | 是 |  | 验证视频 |  |  |
| audit_ip | varchar(64) | 是 |  | 审核ip |  |  |
| audit_time | bigint(19) | 是 |  | 审核时间 |  |  |
| audit_by | varchar(128) | 是 |  | 审核人 |  |  |
| reject_reason | varchar(256) | 是 |  | 驳回原因 |  |  |
| status | tinyint(3) | 否 |  | 状态（0-未审核；1-审核中；2-初级通过；3-高级通过；4-审核失败） | MUL |  |
| archive_flag | tinyint(3) | 否 | 0 | 归档标识（0-未归档；1-已归档） |  |  |
| archive_by | varchar(128) | 是 |  | 归档人 |  |  |
| archive_time | bigint(19) | 是 |  | 归档时间 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_authentication_type | authentication_type | 否 | BTREE |
| idx_certificate_number | certificate_number | 否 | BTREE |
| idx_country_code | country_code | 否 | BTREE |
| idx_current_real_name_level | current_real_name_level | 否 | BTREE |
| idx_real_name | real_name | 否 | BTREE |
| idx_status | status | 否 | BTREE |
| idx_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_real_name_business_cfg

**表注释**: 用户实名业务配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| business_type | varchar(32) | 否 |  | 业务类型 |  |  |
| real_name_level | tinyint(3) | 否 |  | 实名认证等级（0-未认证；1-初级认证；2-高级认证） |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_real_name_business_region_cfg

**表注释**: 用户实名业务区域配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| business_type | varchar(32) | 否 |  | 业务类型 |  |  |
| real_name_level | tinyint(3) | 否 |  | 实名认证等级（0-未认证；1-初级认证；2-高级认证） |  |  |
| region | varchar(256) | 否 |  | 地区 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_sms_record

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI |  |
| send_type | varchar(32) | 否 |  | 发送类型：MsgSendTypeEnum
（
EMAIL：邮箱发送；
PHONE：手机号发送
） | MUL |  |
| sms_type | varchar(32) | 否 |  | 短信类型：MsgTypeEnum
（
REGISTER_CAPTCHA：登录验证码；
LOGIN_CAPTCHA：登录验证码；
TRANSFER_COIN_CAPTCHA：内部转账(币)验证码；
WITHDRAW_COIN_CAPTCHA：提币验证码；
EXCHANGE_COIN_CAPTCHA", "兑换验证码；
WITHDRAW_NFT_CAPTCHA：提现NFT验证码；
SETUP_LOGIN_PASSWORD_CAPTCHA：设置登录密码验证码；
UPDATE_LOGIN_PASSWORD_CAPTCHA：修改登录密码验证码；
SETUP_FUNDS_PASSWORD_CAPTCHA：设置资金密码验证码；
UPDATE_FUNDS_PASSWORD_CAPTCHA：修改资金密码验证码；
BIND_GOOGLE_CODE_CAPTCHA：绑定谷歌校验码验证码；
UNBIND_GOOGLE_CODE_CAPTCHA：解绑谷歌校验码验证码
） | MUL |  |
| sms_platform | varchar(32) | 否 |  | 短信平台：smsPlatformEnum
（
MSG91：MSG91
） | MUL |  |
| uid | int(10) | 是 |  | 用户uid | MUL |  |
| email | varchar(128) | 是 |  | 发送账号 | MUL |  |
| phone_area_code | varchar(32) | 是 |  | 手机区号 |  |  |
| phone | varchar(128) | 是 |  | 手机号 | MUL |  |
| send_content | varchar(512) | 否 |  | 发送内容 |  |  |
| ip_address | varchar(256) | 是 |  | IP地址 |  |  |
| send_status | varchar(32) | 是 |  | 发送状态 |  |  |
| msg | varchar(512) | 是 |  | 响应消息 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_email | email | 否 | BTREE |
| idx_phone | phone | 否 | BTREE |
| idx_send_type | send_type | 否 | BTREE |
| idx_sms_platform | sms_platform | 否 | BTREE |
| idx_sms_type | sms_type | 否 | BTREE |
| idx_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_system_version_cfg

**表注释**: 系统版本配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| client_type | varchar(32) | 否 |  | 客户端类型：ClientTypeEnum
(
GENERAL：通用；
iOS：iOS；
Android：Android；
PC：电脑
) | MUL |  |
| version | int(10) | 否 |  | 版本号 | MUL |  |
| version_name | varchar(32) | 否 |  | 版本名称 | MUL |  |
| force_update_flag | varchar(32) | 否 |  | 强更标识：FlagTypeEnum
(
Y：yes；
N：no
) |  |  |
| update_content_text_key | varchar(64) | 否 |  | 更新内容文本key |  |  |
| download_link | varchar(256) | 否 |  | 下载链接 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_client_type | client_type | 否 | BTREE |
| idx_version | version | 否 | BTREE |
| idx_version_name | version_name | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_wallet_address

**表注释**: 用户钱包地址表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 用户uid | MUL |  |
| address | varchar(1024) | 否 |  | 具体的地址 |  |  |
| is_default | tinyint(3) | 否 |  | 是否默认 |  |  |
| status | tinyint(3) | 否 |  | 状态 0不启用 1-启用 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| index_uid | uid | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

