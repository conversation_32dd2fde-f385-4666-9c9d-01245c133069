# 数据库: mini_guess

**分析时间**: 2025/7/24 18:11:25

**表数量**: 14

**表列表**: day_order, goods_info, goods_sku, goods_type, language_text_cfg, optional_goods, order, report_broker_reward, report_sub_reward, report_team_reward, undo_log, user_grade_reward_cfg, user_reward, user_reward_sum

---

## 表名: day_order

**表注释**: 每日订单汇总

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 用户uid |  |  |
| day_code | varchar(16) | 是 |  | 日期code yyyyMMdd格式 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 收入（包含订单金额） |  |  |
| order_num | int(10) | 否 |  | 订单数 |  |  |
| trade_qty | decimal(36,18) | 否 | 0.000000000000000000 | 订单金额 |  |  |
| win_amount | decimal(36,18) | 否 | 0.000000000000000000 | 盈利金额 |  |  |
| lose_amount | decimal(36,18) | 否 | 0.000000000000000000 | 亏损金额 |  |  |
| remark | varchar(255) | 是 |  | 说明 |  |  |
| count_time | bigint(19) | 否 |  | 统计订单时间 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: goods_info

**表注释**: 商品表;

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI | auto_increment |
| goods_name | varchar(32) | 否 |  | 商品代码 |  |  |
| type | int(10) | 是 |  | 商品类型 |  |  |
| show_precision | int(10) | 否 |  | 币种小数位 |  |  |
| coin | varchar(32) | 否 |  | 币种 |  |  |
| min_qty | decimal(32,16) | 否 |  | 最少投注数量 |  |  |
| incr_qty | decimal(32,16) | 否 |  | 投注增量 |  |  |
| enable_qty | varchar(255) | 否 |  | 可选投注数量,多个用英文逗号分隔 |  |  |
| status | int(10) | 否 |  | 状态 0-下架 1-上架 |  |  |
| sort | int(10) | 是 |  | 排序 |  |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新用户ID |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: goods_sku

**表注释**: 商品规格表;

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 是 |  | 主键 |  |  |
| goods_id | bigint(19) | 是 |  | 商品id |  |  |
| trade_time | int(10) | 否 |  | 时长 单位秒 |  |  |
| odds | decimal(6,4) | 否 |  | 赔率 |  |  |
| status | int(10) | 是 | 1 | 状态 0 -不可用 1可用 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |

---

## 表名: goods_type

**表注释**: 商品分类;

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI | auto_increment |
| name | varchar(32) | 是 |  | 名称 |  |  |
| type | varchar(32) | 是 |  | 商品分类类型 |  |  |
| sort | int(10) | 是 |  | 排序 |  |  |
| status | int(10) | 是 |  | 状态 |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 是 |  | 创建时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新用户ID |  |  |
| update_time | bigint(19) | 是 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: language_text_cfg

**表注释**: 语言文本配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI | auto_increment |
| language_key | varchar(64) | 否 |  | 语言key | MUL |  |
| language_type | varchar(32) | 否 |  | 语言类型:zh_cn-简体中文;zh_hk-繁体中文;en_us-英文 | MUL |  |
| language_text | text(65535) | 否 |  | 语言文本 |  |  |
| business_describe | varchar(512) | 是 |  | 业务描述 |  |  |
| create_by | varchar(128) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 是 |  | 更新用户ID |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_language_key | language_key | 否 | BTREE |
| idx_language_type | language_type | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: optional_goods

**表注释**: 商品自选表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 用户uid |  |  |
| goods_id | bigint(19) | 否 |  | 商品Id |  |  |
| goods_name | varchar(32) | 否 |  | 商品名称 |  |  |
| status | tinyint(3) | 否 |  | 自选状态 0-取消 1-自选 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: order

**表注释**: 订单明细表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| order_code | varchar(64) | 是 |  | 订单编码(预留) | MUL |  |
| uid | int(10) | 否 |  | 用户uid |  |  |
| goods_id | varchar(32) | 否 |  | 商品id | MUL |  |
| goods_name | varchar(64) | 否 |  | 商品名称 |  |  |
| trade_coin | varchar(32) | 否 |  | 交易币种 |  |  |
| trade_time | int(10) | 是 |  | 时长 秒 |  |  |
| trade_qty | decimal(26,18) | 否 |  | 交易数量 |  |  |
| direction | tinyint(3) | 否 |  | 交易方向 0-跌 1-涨 |  |  |
| odds | decimal(8,6) | 否 |  | 赔率 |  |  |
| open_price | decimal(36,18) | 否 |  | 开仓价格 |  |  |
| open_time | bigint(19) | 否 |  | 开仓时间 |  |  |
| amount | decimal(36,18) | 是 | 0.000000000000000000 | 实际收入 |  |  |
| predict_amount | decimal(36,18) | 否 |  | 预计收入 下单金额*赔率 |  |  |
| is_win | tinyint(3) | 是 |  | 平仓结果：0-亏 1-盈 |  |  |
| status | tinyint(3) | 否 |  | 订单状态   -2 已平仓未发放奖励失败 -1 平仓失败 1-已下单  2-已平仓未发放奖励 3-已完成  |  |  |
| close_price | decimal(36,18) | 是 |  | 平仓价格 |  |  |
| close_time | bigint(19) | 是 |  | 平仓时间 |  |  |
| real_close_time | bigint(19) | 是 |  | 实际平仓时间 |  |  |
| remark | varchar(64) | 是 |  | 备注 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| index_goods_sku_id | goods_id | 否 | BTREE |
| index_order_code | order_code | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: report_broker_reward

**表注释**: 经纪人奖励统计表（特殊用户）

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 经纪人uid |  |  |
| day_code | varchar(16) | 是 |  | 日期编号 |  |  |
| team_num | int(10) | 是 | 0 | 团队/家族人数 |  |  |
| trade_num | int(10) | 是 | 0 | 交易人数 |  |  |
| trade_coin | varchar(32) | 是 |  | 交易币种 |  |  |
| order_amount | decimal(36,18) | 否 | 0.000000000000000000 | 交易额 |  |  |
| win_amount | decimal(36,18) | 否 |  | 盈利金额 |  |  |
| lose_amount | decimal(36,18) | 否 |  | 亏损金额 |  |  |
| win_lose_amount | decimal(36,18) | 否 |  | 净盈亏(盈利金额-亏损金额) |  |  |
| begin_time | bigint(19) | 是 |  | 开始时间 |  |  |
| end_time | bigint(19) | 是 |  | 结束时间 |  |  |
| status | tinyint(3) | 是 |  | 状态 0-不可用 1-可用 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: report_sub_reward

**表注释**: 直接邀请奖励统计表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 经纪人uid |  |  |
| sub_uid | int(10) | 否 |  | 直推下级uid |  |  |
| nick_name | varchar(64) | 是 |  | 直推下级昵称 |  |  |
| register_time | bigint(19) | 是 |  | 直推下级注册时间 |  |  |
| reward_coin | varchar(32) | 是 |  | 奖励币种 |  |  |
| reward_qty | decimal(36,18) | 是 | 0.000000000000000000 | 奖励数量 |  |  |
| deposit_amount | decimal(36,18) | 是 | 0.000000000000000000 | 入金数量 |  |  |
| order_amount | decimal(36,18) | 是 | 0.000000000000000000 | 投注数量 |  |  |
| begin_time | bigint(19) | 是 |  | 开始时间 |  |  |
| end_time | bigint(19) | 是 |  | 结束时间 |  |  |
| status | tinyint(3) | 是 | 1 | 状态 0-不可用 1-可用 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: report_team_reward

**表注释**: 团队/家族奖励统计表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 经纪人uid |  |  |
| sub_uid | int(10) | 否 |  | 直推下级uid |  |  |
| reward_coin | varchar(32) | 是 |  | 奖励币种 |  |  |
| reward_qty | decimal(36,18) | 是 | 0.000000000000000000 | 奖励数量 |  |  |
| team_num | int(10) | 是 | 0 | 团队/家族人数 |  |  |
| order_amount | decimal(36,18) | 是 | 0.000000000000000000 | 最近三十天投注数量 |  |  |
| begin_time | bigint(19) | 是 |  | 开始时间 |  |  |
| end_time | bigint(19) | 是 |  | 结束时间 |  |  |
| status | tinyint(3) | 是 |  | 状态 0-不可用 1-可用 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: undo_log

**表注释**: seata分布式事务控制表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| branch_id | bigint(19) | 否 |  | 无注释 |  |  |
| xid | varchar(100) | 否 |  | 无注释 | MUL |  |
| context | varchar(128) | 否 |  | 无注释 |  |  |
| rollback_info | longblob(4294967295) | 否 |  | 无注释 |  |  |
| log_status | int(10) | 否 |  | 无注释 |  |  |
| log_created | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| log_modified | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| ext | varchar(100) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| ux_undo_log | xid | 是 | BTREE |
| ux_undo_log | branch_id | 是 | BTREE |

---

## 表名: user_grade_reward_cfg

**表注释**: 等级权益表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| grade_level | int(10) | 否 |  | 经纪人等级 |  |  |
| sub_level | tinyint(3) | 否 |  | 子级层数：1表示下一级 |  |  |
| reward_rate | decimal(8,6) | 否 |  | 奖励比例 |  |  |
| status | tinyint(3) | 否 |  | 状态 0-不可用 1-可用 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_reward

**表注释**: 邀请奖励详情表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| order_code | varchar(32) | 否 |  | 奖励来源订单编号 | MUL |  |
| uid | int(10) | 否 |  | 奖励用户uid |  |  |
| direct_uid | int(10) | 是 |  | 奖励用户的直推用户uid  |  |  |
| trade_coin | varchar(32) | 否 |  | 交易币种 |  |  |
| trade_qty | decimal(36,18) | 否 |  | 交易数量 |  |  |
| sub_level | tinyint(3) | 否 |  | 子级层数 |  |  |
| sub_uid | int(10) | 否 |  | 子级uid |  |  |
| reward_coin | varchar(32) | 否 |  | 奖励币种 |  |  |
| reward_qty | decimal(36,18) | 否 |  | 奖励数量 |  |  |
| reward_rate | decimal(8,6) | 否 |  | 奖励比例 |  |  |
| reward_time | bigint(19) | 是 |  | 奖励发放时间 |  |  |
| status | tinyint(3) | 否 |  | 状态 0-待处理 1-已处理 2-处理失败 3-重试失败(需人工介入) |  |  |
| remark | varchar(64) | 是 |  | 备注 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| index_orderCode | order_code | 是 | BTREE |
| index_orderCode | uid | 是 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: user_reward_sum

**表注释**: 用户奖励汇总表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 经纪人uid |  |  |
| level | tinyint(3) | 否 |  | 经纪人等级 |  |  |
| direct_num | int(10) | 否 | 0 | 直推用户人数 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 共计奖励数量 |  |  |
| direct_amount | decimal(36,18) | 否 | 0.000000000000000000 | 直推用户奖励金额 |  |  |
| indirect_amount | decimal(36,18) | 否 | 0.000000000000000000 | 非直推用户奖励金额 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

