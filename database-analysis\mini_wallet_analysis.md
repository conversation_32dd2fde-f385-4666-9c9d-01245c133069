# 数据库: mini_wallet

**分析时间**: 2025/7/24 18:11:26

**表数量**: 7

**表列表**: undo_log, wallet_block_scanner, wallet_in_record, wallet_info, wallet_nft_deploy_info, wallet_nft_mint_info, wallet_out_record

---

## 表名: undo_log

**表注释**: seata分布式事务控制表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| branch_id | bigint(19) | 否 |  | 无注释 |  |  |
| xid | varchar(100) | 否 |  | 无注释 | MUL |  |
| context | varchar(128) | 否 |  | 无注释 |  |  |
| rollback_info | longblob(4294967295) | 否 |  | 无注释 |  |  |
| log_status | int(10) | 否 |  | 无注释 |  |  |
| log_created | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| log_modified | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| ext | varchar(100) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| ux_undo_log | xid | 是 | BTREE |
| ux_undo_log | branch_id | 是 | BTREE |

---

## 表名: wallet_block_scanner

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| block_hash | varchar(256) | 是 |  | 无注释 |  |  |
| block_height | bigint(19) | 否 |  | 无注释 |  |  |
| locked | int(10) | 否 | 0 | 0-未锁定;1-锁定 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: wallet_in_record

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| record_id | bigint(19) | 否 |  | 使用雪花算法生成主键ID | PRI |  |
| transaction_id | varchar(128) | 否 |  | 关联充值业务ID |  |  |
| transaction_type | int(10) | 否 |  | 1-客户充值；2-找零；3-链费充值； |  |  |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| coin | varchar(32) | 是 |  | 如果充值的是币，必填 |  |  |
| token_id | varchar(256) | 是 |  | 如果充值的是NFT，必填 |  |  |
| from_address | varchar(256) | 否 |  | 无注释 |  |  |
| to_address | varchar(256) | 否 |  | 无注释 |  |  |
| amount | decimal(36,18) | 否 |  | 无注释 |  |  |
| txid | varchar(256) | 否 |  | 无注释 |  |  |
| status | int(10) | 否 |  | 1-待确认；2-已确认；3-确认失败； |  |  |
| confirm_num | int(10) | 是 |  | 无注释 |  |  |
| confirm_time | timestamp | 是 |  | 无注释 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 无注释 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |
| log_index | varchar(32) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | record_id | 是 | BTREE |

---

## 表名: wallet_info

**表注释**: 钱包地址信息

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| wallet_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| user_wallet_id | bigint(19) | 是 |  | 关联assets_wallet_info表wallet_id字段 |  |  |
| tenant_id | bigint(19) | 否 | 0 | 无注释 |  |  |
| app_id | bigint(19) | 否 |  | 无注释 |  |  |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| coin | varchar(32) | 是 |  | 无注释 |  |  |
| user_id | varchar(32) | 否 |  | 如果用户类型为客户，则保存客户ID，如果客户类型为商户，则保存商户ID |  |  |
| user_type | int(10) | 否 |  | 1-客户；2-商户 |  |  |
| wallet_address | varchar(256) | 否 |  | 无注释 |  |  |
| public_key | varchar(3000) | 是 |  | 加密存储 |  |  |
| private_key | varchar(3000) | 是 |  | 加密存储 |  |  |
| go_private_key | varchar(3000) | 是 |  | 加密存储 |  |  |
| is_compress | int(10) | 是 |  | 0-不压缩；1-压缩 |  |  |
| mnemonic | varchar(1024) | 是 |  | 加密存储 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| status | int(10) | 是 |  | 0-禁用;1-可用 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | wallet_id | 是 | BTREE |

---

## 表名: wallet_nft_deploy_info

**表注释**: NFT合约deploy信息

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| deploy_id | bigint(19) | 否 |  | 合约ID | PRI | auto_increment |
| app_id | bigint(19) | 否 |  | AppID |  |  |
| tenant_id | bigint(19) | 否 | 0 | 商户ID |  |  |
| transaction_id | bigint(19) | 否 |  | 关联业务ID, 关联到waas库里面的nft_deploy_info 的id |  |  |
| chain | varchar(32) | 否 |  | 链名称 |  |  |
| protocol | varchar(32) | 是 |  | 协议名称 |  |  |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |
| collection_name | varchar(256) | 否 |  | 合约名称 |  |  |
| collection_symbol | varchar(256) | 否 |  | 合约名称标志 |  |  |
| max_item_qty | int(10) | 是 |  | 作品最大数量:1024 |  |  |
| contract_creator | varchar(256) | 否 |  | 创造者地址 |  |  |
| confirm_num | int(10) | 是 |  | 无注释 |  |  |
| confirm_time | timestamp | 是 |  | 无注释 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 无注释 |  |  |
| txid | varchar(256) | 否 |  | 无注释 |  |  |
| status | int(10) | 否 |  | 1-待确认；2-已确认；3-确认失败； |  |  |
| gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| gas_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 是 |  | 管理员 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| collection_template_id | bigint(19) | 是 |  | 合约模板ID |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | deploy_id | 是 | BTREE |

---

## 表名: wallet_nft_mint_info

**表注释**: NFTmint表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| mint_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| transaction_id | bigint(19) | 是 |  | 业务ID，保存waas库里面nft_mint_info的mint_id |  |  |
| app_id | bigint(19) | 否 |  | APPID |  |  |
| tenant_id | bigint(19) | 否 | 0 | 商户ID |  |  |
| chain | varchar(32) | 否 |  | 链名称 |  |  |
| protocol | varchar(32) | 是 |  | 协议名称 |  |  |
| contract_address | varchar(256) | 否 |  | 合约地址 |  |  |
| item_name | varchar(256) | 否 |  | 作品名称 |  |  |
| token_id | varchar(256) | 是 |  | TokenId |  |  |
| qty | decimal(36,18) | 否 | 1.000000000000000000 | 默认1，ERC1155协议半同质化可以大于1 |  |  |
| mint_addr | varchar(256) | 是 |  | 链上创建者地址 |  |  |
| mintor_type | int(10) | 否 |  | 持有者类型1-客户；2-商户 |  |  |
| mintor_user_id | varchar(32) | 否 |  | 根据持有者类型，填写商户ID或者客户ID |  |  |
| item_url | varchar(256) | 否 |  | 作品URL |  |  |
| metadata_url | varchar(256) | 否 |  | 元数据URL |  |  |
| file_suffix | varchar(255) | 是 |  | 文件后缀 |  |  |
| description | varchar(1024) | 是 |  | 描述json |  |  |
| confirm_num | int(10) | 是 |  | 无注释 |  |  |
| confirm_time | timestamp | 是 |  | 无注释 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 无注释 |  |  |
| txid | varchar(256) | 是 |  | 无注释 |  |  |
| status | int(10) | 否 | 0 | 1-待确认；2-已确认；3-确认失败； |  |  |
| gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| gas_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_user | varchar(64) | 是 |  | 管理员 |  |  |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | mint_id | 是 | BTREE |

---

## 表名: wallet_out_record

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| record_id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| transaction_id | varchar(128) | 否 |  | 关联提币业务订单ID |  |  |
| transaction_type | int(10) | 否 |  | 1-客户提币；2-商户提币； |  |  |
| chain | varchar(32) | 否 |  | 无注释 |  |  |
| protocol | varchar(32) | 是 |  | 无注释 |  |  |
| coin | varchar(32) | 是 |  | 币提现，必填 |  |  |
| token_id | varchar(256) | 是 |  | NFT提现，必填 |  |  |
| from_address | varchar(256) | 否 |  | 无注释 |  |  |
| to_address | varchar(256) | 否 |  | 无注释 |  |  |
| amount | decimal(36,18) | 否 |  | 无注释 |  |  |
| txid | varchar(256) | 是 |  | 无注释 |  |  |
| status | int(10) | 否 |  | 0-未处理；1-待确认；2-已确认；3-确认失败； |  |  |
| confirm_num | int(10) | 是 |  | 无注释 |  |  |
| confirm_time | timestamp | 是 |  | 无注释 |  |  |
| tx_fee | decimal(36,18) | 是 |  | 无注释 |  |  |
| create_time | timestamp | 否 | CURRENT_TIMESTAMP | 创建时间 |  | DEFAULT_GENERATED |
| update_time | timestamp | 否 | CURRENT_TIMESTAMP | 更新时间 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| gas_price | decimal(36,18) | 是 |  | 无注释 |  |  |
| gas_limit | decimal(36,18) | 是 |  | 无注释 |  |  |
| contract_address | varchar(256) | 是 |  | 合约地址 |  |  |
| log_index | varchar(32) | 是 |  | 无注释 |  |  |
| nonce | varchar(64) | 是 |  | nonce值存储 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | record_id | 是 | BTREE |

---

