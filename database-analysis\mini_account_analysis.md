# 数据库: mini_account

**分析时间**: 2025/7/24 18:11:24

**表数量**: 15

**表列表**: ac_change_type, ac_coin_account, ac_coin_account_change_log, ac_coin_account_his, ac_coin_deposit, ac_coin_info, ac_coin_internal_adjust, ac_coin_withdraw, ac_day_deposit, ac_language_text_cfg, ac_nft_account, ac_nft_account_change_log, ac_task_reward_sum, ac_user_account, undo_log

---

## 表名: ac_change_type

**表注释**: 资金调整类型

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | int(10) | 否 |  | 自增ID | PRI | auto_increment |
| change_type | int(10) | 否 |  | 变动类型/小类，与代码强绑定不可更改 |  |  |
| change_name | varchar(255) | 否 |  | 管理端展示名称 |  |  |
| show_name | varchar(255) | 否 |  | 客户端展示名称 |  |  |
| show_status | int(10) | 否 | 1 | 客户端是否显示：0-不显示；1-显示 |  |  |
| relation_type | int(10) | 否 |  | 关联类型/大类 |  |  |
| relation_name | varchar(255) | 否 |  | 关联类型显示名称 |  |  |
| show_type | int(10) | 否 |  | 适用方：0-平台；1-客户端 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_coin_account

**表注释**: 币币账户表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增ID | PRI | auto_increment |
| account_id | bigint(19) | 否 |  | 账户ID | MUL |  |
| uid | int(10) | 否 |  | 用户ID |  |  |
| coin | varchar(32) | 否 |  | 币种名称 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 余额 |  |  |
| amount_type | int(10) | 否 |  | 余额类型，对应账户类型 |  |  |
| revision | int(10) | 否 | 0 | 乐观锁 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uidx_account_uid_coin_amounttype | account_id | 是 | BTREE |
| uidx_account_uid_coin_amounttype | uid | 是 | BTREE |
| uidx_account_uid_coin_amounttype | coin | 是 | BTREE |
| uidx_account_uid_coin_amounttype | amount_type | 是 | BTREE |

---

## 表名: ac_coin_account_change_log

**表注释**: 币币账户变动日志表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增主键 | PRI |  |
| account_id | bigint(19) | 否 |  | 账户ID | MUL |  |
| uid | int(10) | 否 |  | 用户ID |  |  |
| coin | varchar(32) | 否 |  | 币种名称 |  |  |
| change_type | int(10) | 否 |  | 变动类型/小类 |  |  |
| before_amount | decimal(36,18) | 否 |  | 变动前金额 |  |  |
| amount | decimal(36,18) | 否 |  | 变动金额(+-) |  |  |
| after_amount | decimal(36,18) | 否 |  | 变动后金额 |  |  |
| amount_type | int(10) | 是 |  | 金额类型 |  |  |
| relation_id | bigint(19) | 否 | 0 | 关联表ID |  |  |
| relation_no | varchar(64) | 否 | 0 | 关联业务号，用于显示给用户看 |  |  |
| relation_type | int(10) | 否 | 0 | 关联类型:关联到对应的库表:1-CHAIN_COIN_DEPOSIT;2-CHAIN_COIN_WITHDRAW;3-CHAIN_COIN_INTERNAL_TRANSFER;4-CHAIN_COIN_EXCHANGE;5-AC_COIN_INTERNAL_ADJUST;6-CHAIN_NFT_DEPOSIT;7-CHAIN_NFT_WITHDRAW;8-OD_AD_VIEW_LOG;9-OD_ORDER |  |  |
| remark | varchar(255) | 是 |  | 备注 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uidx_uid_acct_coin_chtype_amtype_relatno | account_id | 是 | BTREE |
| uidx_uid_acct_coin_chtype_amtype_relatno | uid | 是 | BTREE |
| uidx_uid_acct_coin_chtype_amtype_relatno | coin | 是 | BTREE |
| uidx_uid_acct_coin_chtype_amtype_relatno | change_type | 是 | BTREE |
| uidx_uid_acct_coin_chtype_amtype_relatno | relation_no | 是 | BTREE |
| uidx_uid_acct_coin_chtype_amtype_relatno | amount_type | 是 | BTREE |

---

## 表名: ac_coin_account_his

**表注释**: 币币账户历史表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增ID | PRI | auto_increment |
| coin_acct_id | bigint(19) | 否 |  | 代币账户表ID |  |  |
| account_id | bigint(19) | 否 |  | 账户ID |  |  |
| uid | int(10) | 否 |  | 用户ID |  |  |
| coin | varchar(32) | 否 |  | 币种名称 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 余额 |  |  |
| amount_type | int(10) | 否 |  | 余额类型 |  |  |
| revision | int(10) | 否 | 0 | 乐观锁 |  |  |
| create_by | varchar(32) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(32) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |
| statistical_time | bigint(19) | 否 |  | 统计时间戳，同一次统计该值相同 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_coin_deposit

**表注释**: 币链上充值

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| to_uid | int(10) | 是 |  | touid |  |  |
| chain | varchar(255) | 否 |  | 链 |  |  |
| protocol | varchar(255) | 是 |  | 协议 |  |  |
| coin | varchar(255) | 是 |  | 币种 |  |  |
| amount | decimal(36,18) | 是 |  | 充值数量 |  |  |
| up_account | int(10) | 是 |  | 是否上账;0 - 未上账， 1 - 已上账 |  |  |
| from_address | varchar(255) | 是 |  | from地址 |  |  |
| to_address | varchar(255) | 是 |  | to地址 |  |  |
| tx_id | varchar(255) | 是 |  | 链上交易id |  |  |
| create_by | varchar(255) | 是 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 是 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_coin_info

**表注释**: 币信息 支付平台配置使用

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| coin | varchar(255) | 否 |  | 币种 |  |  |
| coin_icon | varchar(255) | 是 |  | icon |  |  |
| show_precision | int(10) | 否 |  | 精度 |  |  |
| status | int(10) | 否 | 1 | 支付平台是否上架 |  |  |
| default_show | int(10) | 否 | 0 | 未登录资产列表是否显示：0-不显示；1-显示 |  |  |
| is_register_give | int(10) | 否 | 0 | 是否用户注册赠送币种 |  |  |
| register_amount | decimal(36,18) | 否 | 0.000000000000000000 | 注册赠送数量 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_coin_internal_adjust

**表注释**: 内部调账

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI |  |
| transfer_no | varchar(255) | 否 |  | 业务号 |  |  |
| account_type | int(10) | 否 |  | 账户类型:1-代币；2-NFT；3-法币 |  |  |
| coin | varchar(32) | 否 |  | 币种 |  |  |
| from_uid | bigint(19) | 否 |  | 源UID |  |  |
| to_uid | bigint(19) | 否 |  | 目标UID |  |  |
| amount | decimal(36,18) | 否 |  | 数量 |  |  |
| operation_type | int(10) | 否 |  | 操作类型/资金流水类型 |  |  |
| status | int(10) | 否 | 0 | 0-待审核；1-审核通过；2-审核拒绝 |  |  |
| apply_reason | varchar(1024) | 是 |  | 申请原因 |  |  |
| apply_time | bigint(19) | 否 |  | 申请时间 |  |  |
| apply_by | varchar(255) | 是 |  | 申请人 |  |  |
| audit_comments | varchar(1024) | 是 |  | 审核意见 |  |  |
| audit_by | varchar(255) | 是 |  | 审核人 |  |  |
| audit_time | bigint(19) | 是 |  | 审核时间 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_coin_withdraw

**表注释**: 币链上提现

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| order_id | bigint(19) | 是 |  | 订单id |  |  |
| from_uid | int(10) | 否 |  | 提现uid |  |  |
| chain | varchar(255) | 否 |  | 链 |  |  |
| protocol | varchar(255) | 是 |  | 协议 |  |  |
| coin | varchar(255) | 否 |  | 币 |  |  |
| amount | decimal(36,18) | 否 |  | 数量 |  |  |
| fee | decimal(36,18) | 否 |  | 手续费 |  |  |
| is_min_withdraw | int(10) | 否 |  | 是否是小额提币;0 - 不是， 1 - 是 |  |  |
| from_address | varchar(255) | 是 |  | from地址 |  |  |
| to_address | varchar(255) | 否 |  | to地址 |  |  |
| tx_id | varchar(255) | 是 |  | 链上交易id |  |  |
| status | int(10) | 否 |  | 0-待处理 1-进行中 2-成功 3-失败 |  |  |
| remark | varchar(255) | 是 |  | 说明 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_day_deposit

**表注释**: 用户每日充币统计表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | id | PRI |  |
| uid | int(10) | 否 |  | 提现uid |  |  |
| day_code | varchar(16) | 是 |  | 日期code yyyyMMdd格式 |  |  |
| coin | varchar(255) | 否 |  | 币 |  |  |
| amount | decimal(36,18) | 否 |  | 数量 |  |  |
| count_num | int(10) | 否 |  | 充币订单数量 |  |  |
| status | int(10) | 否 | 1 | 1-完成 |  |  |
| count_time | bigint(19) | 否 |  | 统计订单时间 |  |  |
| remark | varchar(255) | 是 |  | 说明 |  |  |
| create_by | varchar(255) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(255) | 否 |  | 最后修改人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_language_text_cfg

**表注释**: 语言文本配置表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 主键 | PRI | auto_increment |
| language_key | varchar(64) | 否 |  | 语言key | MUL |  |
| language_type | varchar(32) | 否 |  | 语言类型:zh_cn-简体中文;zh_hk-繁体中文;en_us-英文 | MUL |  |
| language_text | text(65535) | 否 |  | 语言文本 |  |  |
| business_describe | varchar(512) | 是 |  | 业务描述 |  |  |
| create_by | varchar(128) | 否 | SYSTEM | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 | SYSTEM | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| idx_language_key | language_key | 否 | BTREE |
| idx_language_type | language_type | 否 | BTREE |
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_nft_account

**表注释**: NFT账户表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增ID | PRI | auto_increment |
| account_id | bigint(19) | 是 |  | 无注释 |  |  |
| uid | int(10) | 否 |  | 用户ID |  |  |
| item_id | bigint(19) | 否 |  | 作品ID |  |  |
| item_name | varchar(255) | 否 |  | 币种名称 |  |  |
| token_id | varchar(255) | 否 |  | TokenID |  |  |
| collection_id | bigint(19) | 否 |  | 系列ID |  |  |
| collection_name | varchar(255) | 否 |  | 系列名称 |  |  |
| owner_address | varchar(255) | 否 |  | 拥有者地址，用于用户更换地址用 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 可用数量 |  |  |
| amount_type | int(10) | 否 |  | 金额类型 |  |  |
| revision | int(10) | 否 | 0 | 乐观锁 |  |  |
| create_by | varchar(32) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(32) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_nft_account_change_log

**表注释**: NFT账户变动日志表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 自增主键 | PRI |  |
| account_id | bigint(19) | 否 |  | 账户ID | MUL |  |
| uid | int(10) | 否 |  | 用户ID |  |  |
| item_id | bigint(19) | 否 |  | 作品ID |  |  |
| item_name | varchar(255) | 否 |  | NFT作品名称 |  |  |
| token_id | varchar(255) | 否 |  | TokenId |  |  |
| collection_id | bigint(19) | 否 |  | 系列ID |  |  |
| collection_name | varchar(255) | 否 |  | 系列名称 |  |  |
| owner_address | varchar(255) | 否 |  | 系列名称 |  |  |
| change_type | int(10) | 否 |  | 变动类型 |  |  |
| before_amount | decimal(36,18) | 否 |  | 变动前数量 |  |  |
| amount | decimal(36,18) | 否 |  | 变动数量(+-) |  |  |
| after_amount | decimal(36,18) | 否 |  | 变动后数量 |  |  |
| amount_type | int(10) | 否 |  | 金额类型 |  |  |
| relation_id | bigint(19) | 否 | 0 | 关联表ID |  |  |
| relation_no | varchar(64) | 否 | 0 | 关联业务号，用于显示给用户看 |  |  |
| relation_type | int(10) | 否 | 0 | 关联表 |  |  |
| remark | varchar(255) | 是 |  | 备注 |  |  |
| create_by | varchar(32) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | account_id | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | uid | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | item_id | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | token_id | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | collection_id | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | change_type | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | amount_type | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | relation_no | 是 | BTREE |
| uidx_uid_item_collection_chgtype_amttype_addr | owner_address | 是 | BTREE |

---

## 表名: ac_task_reward_sum

**表注释**: 用户任务奖励累计表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | ID | PRI |  |
| uid | int(10) | 否 |  | 用户ID |  |  |
| task_key | varchar(64) | 否 |  | 任务key |  |  |
| coin | varchar(32) | 否 |  | 奖励币种 |  |  |
| coin_type | int(10) | 否 |  | 奖励币种类型 |  |  |
| amount | decimal(36,18) | 否 | 0.000000000000000000 | 累计金额 |  |  |
| revision | int(10) | 否 | 0 | 乐观锁 |  |  |
| create_by | varchar(128) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(128) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: ac_user_account

**表注释**: 用户账户信息

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 账户ID | PRI |  |
| uid | int(10) | 否 |  | 用户ID |  |  |
| account_type | int(10) | 否 |  | 账户类型:1-代币；2-NFT；3-法币；4-积分 |  |  |
| status | int(10) | 否 | 1 | 账户状态 |  |  |
| create_by | varchar(32) | 否 |  | 创建人 |  |  |
| create_time | bigint(19) | 否 |  | 创建时间 |  |  |
| update_by | varchar(32) | 否 |  | 更新人 |  |  |
| update_time | bigint(19) | 否 |  | 更新时间 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |

---

## 表名: undo_log

**表注释**: seata分布式事务控制表

### 字段信息

| 字段名 | 数据类型 | 是否为空 | 默认值 | 字段注释 | 键类型 | 额外信息 |
|--------|----------|----------|--------|----------|--------|----------|
| id | bigint(19) | 否 |  | 无注释 | PRI | auto_increment |
| branch_id | bigint(19) | 否 |  | 无注释 |  |  |
| xid | varchar(100) | 否 |  | 无注释 | MUL |  |
| context | varchar(128) | 否 |  | 无注释 |  |  |
| rollback_info | longblob(4294967295) | 否 |  | 无注释 |  |  |
| log_status | int(10) | 否 |  | 无注释 |  |  |
| log_created | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| log_modified | timestamp | 否 | CURRENT_TIMESTAMP | 无注释 |  | DEFAULT_GENERATED |
| ext | varchar(100) | 是 |  | 无注释 |  |  |

### 索引信息

| 索引名 | 字段名 | 是否唯一 | 索引类型 |
|--------|--------|----------|----------|
| PRIMARY | id | 是 | BTREE |
| ux_undo_log | xid | 是 | BTREE |
| ux_undo_log | branch_id | 是 | BTREE |

---

