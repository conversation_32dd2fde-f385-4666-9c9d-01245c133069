const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库连接配置
const dbConfig = {
  host: '*************',
  port: 3306,
  user: 'root',
  password: 'Nod%D*h2y2*oUZuz',
  charset: 'utf8mb4'
};

// 创建输出目录
const outputDir = './database-analysis';

// 获取所有数据库
async function getAllDatabases(connection) {
  try {
    const [rows] = await connection.query('SHOW DATABASES');
    const systemDbs = ['information_schema', 'performance_schema', 'mysql', 'sys'];
    const databases = rows
      .map(row => row.Database)
      .filter(db => !systemDbs.includes(db) && !db.toLowerCase().includes('mock'));
    
    return databases;
  } catch (error) {
    console.error('获取数据库列表失败:', error);
    return [];
  }
}

// 获取数据库中的所有表
async function getTablesInDatabase(connection, database) {
  try {
    await connection.query(`USE \`${database}\``);
    const [rows] = await connection.query('SHOW TABLES');
    const tableKey = `Tables_in_${database}`;
    return rows.map(row => row[tableKey]);
  } catch (error) {
    console.error(`获取数据库 ${database} 的表列表失败:`, error);
    return [];
  }
}

// 获取表的详细信息
async function getTableDetails(connection, database, tableName) {
  try {
    const [columns] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT,
        COLUMN_KEY,
        EXTRA,
        CHARACTER_MAXIMUM_LENGTH,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [database, tableName]);

    // 获取表注释
    const [tableInfo] = await connection.execute(`
      SELECT TABLE_COMMENT 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
    `, [database, tableName]);

    const missingComments = columns.filter(col => 
      !col.COLUMN_COMMENT || col.COLUMN_COMMENT.trim() === '' || col.COLUMN_COMMENT === '无注释'
    );

    return {
      database,
      tableName,
      tableComment: tableInfo[0]?.TABLE_COMMENT || '',
      missingComments
    };
  } catch (error) {
    console.error(`获取表 ${database}.${tableName} 详细信息失败:`, error);
    return null;
  }
}

// 根据字段名和类型生成建议的注释
function generateSuggestedComment(columnName, dataType, columnKey) {
  const name = columnName.toLowerCase();
  
  // 主键
  if (columnKey === 'PRI' && (name.includes('id') || name === 'id')) {
    return '主键ID';
  }
  
  // 常见字段模式
  const patterns = {
    'create_time': '创建时间',
    'update_time': '更新时间',
    'create_by': '创建人',
    'update_by': '更新人',
    'del_flag': '删除标志',
    'status': '状态',
    'type': '类型',
    'name': '名称',
    'code': '编码',
    'sort': '排序',
    'order': '排序',
    'remark': '备注',
    'desc': '描述',
    'description': '描述',
    'phone': '电话号码',
    'email': '邮箱',
    'address': '地址',
    'url': '链接地址',
    'path': '路径',
    'file': '文件',
    'image': '图片',
    'pic': '图片',
    'avatar': '头像',
    'password': '密码',
    'token': '令牌',
    'key': '键',
    'value': '值',
    'amount': '金额',
    'price': '价格',
    'count': '数量',
    'num': '数量',
    'level': '等级',
    'grade': '等级',
    'version': '版本',
    'tenant_id': '租户ID',
    'user_id': '用户ID',
    'uid': '用户ID',
    'parent_id': '父级ID',
    'dept_id': '部门ID',
    'role_id': '角色ID',
    'menu_id': '菜单ID'
  };
  
  // 检查字段名是否包含常见模式
  for (const [pattern, comment] of Object.entries(patterns)) {
    if (name.includes(pattern)) {
      return comment;
    }
  }
  
  // 根据数据类型推测
  if (dataType.includes('time') || dataType.includes('date')) {
    return '时间';
  }
  
  if (dataType.includes('text') || dataType.includes('varchar')) {
    return '文本内容';
  }
  
  if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float')) {
    return '数值';
  }
  
  return '请补充注释';
}

// 生成ALTER TABLE语句
function generateAlterTableSQL(tableDetails) {
  const { database, tableName, tableComment, missingComments } = tableDetails;
  
  let sql = `-- 数据库: ${database}\n`;
  sql += `-- 表: ${tableName}\n`;
  if (tableComment) {
    sql += `-- 表注释: ${tableComment}\n`;
  } else {
    sql += `-- ⚠️ 表缺少注释，建议添加表注释\n`;
    sql += `-- ALTER TABLE \`${database}\`.\`${tableName}\` COMMENT = '请补充表注释';\n`;
  }
  sql += `\n`;
  
  if (missingComments.length === 0) {
    sql += `-- 该表所有字段都有注释，无需修改\n\n`;
    return sql;
  }
  
  sql += `-- 补充缺失的字段注释\n`;
  sql += `USE \`${database}\`;\n\n`;
  
  missingComments.forEach(column => {
    const suggestedComment = generateSuggestedComment(column.COLUMN_NAME, column.DATA_TYPE, column.COLUMN_KEY);
    
    // 构建完整的列定义
    let columnDef = `\`${column.COLUMN_NAME}\` ${column.DATA_TYPE}`;
    
    // 添加长度信息
    if (column.CHARACTER_MAXIMUM_LENGTH) {
      columnDef += `(${column.CHARACTER_MAXIMUM_LENGTH})`;
    } else if (column.NUMERIC_PRECISION) {
      columnDef += `(${column.NUMERIC_PRECISION}${column.NUMERIC_SCALE ? ',' + column.NUMERIC_SCALE : ''})`;
    }
    
    // 添加NULL/NOT NULL
    columnDef += column.IS_NULLABLE === 'YES' ? ' NULL' : ' NOT NULL';
    
    // 添加默认值
    if (column.COLUMN_DEFAULT !== null) {
      if (column.COLUMN_DEFAULT === 'CURRENT_TIMESTAMP') {
        columnDef += ` DEFAULT CURRENT_TIMESTAMP`;
      } else {
        columnDef += ` DEFAULT '${column.COLUMN_DEFAULT}'`;
      }
    }
    
    // 添加额外信息
    if (column.EXTRA) {
      columnDef += ` ${column.EXTRA}`;
    }
    
    // 添加注释
    columnDef += ` COMMENT '${suggestedComment}'`;
    
    sql += `ALTER TABLE \`${tableName}\` MODIFY COLUMN ${columnDef};\n`;
  });
  
  sql += `\n`;
  return sql;
}

// 主函数
async function generateCommentSQL() {
  let connection;
  
  try {
    console.log('开始连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功!');
    
    const databases = await getAllDatabases(connection);
    console.log(`发现数据库: ${databases.join(', ')}`);
    
    let allSQL = `-- MySQL数据库字段注释补充SQL脚本\n`;
    allSQL += `-- 生成时间: ${new Date().toLocaleString()}\n`;
    allSQL += `-- 数据库服务器: ${dbConfig.host}:${dbConfig.port}\n\n`;
    allSQL += `-- 使用说明:\n`;
    allSQL += `-- 1. 请仔细检查每个建议的注释是否准确\n`;
    allSQL += `-- 2. 根据实际业务需求修改注释内容\n`;
    allSQL += `-- 3. 执行前请备份数据库\n`;
    allSQL += `-- 4. 建议分批执行，避免长时间锁表\n\n`;
    allSQL += `-- ========================================\n\n`;
    
    for (const database of databases) {
      console.log(`\n处理数据库: ${database}`);
      
      const tables = await getTablesInDatabase(connection, database);
      console.log(`发现 ${tables.length} 个表`);
      
      let databaseSQL = `-- ========================================\n`;
      databaseSQL += `-- 数据库: ${database}\n`;
      databaseSQL += `-- ========================================\n\n`;
      
      let hasChanges = false;
      
      for (const tableName of tables) {
        console.log(`  处理表: ${tableName}`);
        const tableDetails = await getTableDetails(connection, database, tableName);
        
        if (tableDetails && tableDetails.missingComments.length > 0) {
          hasChanges = true;
          databaseSQL += generateAlterTableSQL(tableDetails);
        }
      }
      
      if (hasChanges) {
        allSQL += databaseSQL;
      } else {
        allSQL += `-- 数据库 ${database} 的所有表字段都有注释，无需修改\n\n`;
      }
    }
    
    // 保存SQL文件
    const sqlPath = path.join(outputDir, 'add-missing-comments.sql');
    await fs.writeFile(sqlPath, allSQL, 'utf8');
    console.log(`\nSQL脚本已生成: ${sqlPath}`);
    
    console.log('\n生成完成!');
    
  } catch (error) {
    console.error('生成过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行生成
if (require.main === module) {
  generateCommentSQL().catch(console.error);
}

module.exports = { generateCommentSQL };
